class Browser {
	constructor(Callback) {
		this.URL = false;

		this.App = false;

		this.Page = false;

		this.Window = false;

		this.AppWindow = false;

		this.ReadyState = false;

		this.NavigationLevel = false;

		this.TransitionState = false;

		this.NavigationWarningParams = false;

		// Theme management properties
		this.ThemeKey = "system-theme";

		this.Initialize(Callback);
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Initialize the Class
	 ** ==================================
	 */

	Initialize(Callback) {
		// Set the default navigation level as browser
		// The navigation level tells us if we're in an app or in the browser index file
		this.NavigationLevel = "browser";

		// Define the browser level window globally
		this.Window = window.top.document;

		// Import browser templates
		TL.ImportTemplate("lib/templates/sys.apps.html?v=2", function () {
			// Make our initial call to the browser system agent
			TL.Agent({
				agent: ["sys", "browser"],
				data: {},
				success: function (Result) {
					Result = JSON.parse(Result);

					// Dev note: We can probably go through all these globally defined variables and set them automatically

					// Establish the apps object globally
					TL["Apps"] = Result["Apps"];

					// Establish the user object globally
					TL["User"] = Result["User"];

					// Establish the users object globally
					TL["Users"] = Result["Users"];

					// Establish the stores object globally
					TL["Stores"] = Result["Stores"];

					// Establish pay weeks object globally
					TL["EffectiveDates"] = Result["EffectiveDates"];

					// Establish the stores object globally
					TL["Job Titles"] = Result["Job Titles"];

					// Establish the permission groups object globally
					TL["Permission Groups"] = Result["Permission Groups"];

					// Establish the inventory status globally
					TL["Inventory Status"] = Result["InventoryStatus"] ?? "DISABLED";

					TL["SiteConfig"] = Result["SiteConfig"];

					// Listen for when the virtual browser has completed loading the page
					// ** Note: Because Safari doesn't recognize iFrame.onload, we cannot use this
					// ** Note: Reference teamlyders.js for caller
					//document.querySelector('iframe[name="TL-Browser"]').onload = TL.Browser.Loaded;

					// Manipulate user data to include more params
					TL.Browser.BindUserData();

					// Construct home screen apps
					TL.Browser.ConstructApps();

					// Navigate to the initial page from the URL
					TL.Browser.Navigate(TL.Location.Slug);

					// Refresh daily summary
					TL.Browser.RefreshDailySummary();

					// Refresh tasks board
					TL.Browser.RefreshTasks();

					// Initialize ping service
					TL.Browser.InitializePing(Result.PingInterval);

					// Initialize theme system
					TL.Browser.InitializeTheme();

					// Let the system know, we're good to go
					TL.Browser.ReadyState = true;

					// Send to package callback
					if (typeof Callback == "function") {
						Callback();
					}
				},
			});
		});
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Browser OnLoad Callback
	 ** ==================================
	 */

	Loaded() {
		// Define a global shortcut variable to the virtual document
		TL.Browser.AppWindow = window.top.frames["TL-Browser"].document;

		// Retrieve the URL path that was actually loaded
		// The path that was requested won't necessarily be the path that's returned
		// Ex: Navigating to login while logged in will actually load home
		let LoadedPath = TL.Browser.AppWindow.querySelector('meta[name="path"]').getAttribute("value");

		// Clean up the loaded path defined above - make it all pretty
		// We do this because this path will be displayed in the browser's URL
		let CleanPath = LoadedPath.replace(".php", "").replace("/index", "/");

		// Set the the location that was actually loaded
		TL.Browser.SetLocation(CleanPath);

		// End the page transition animation
		TL.Browser.PageTransition(false);

		// End the loading bar animation
		TL.Browser.Loading(false);
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Set the Browser URL Path & History
	 ** ==================================
	 */

	SetLocation(RequestURL) {
		// Clean URL by removing search params for app/page extraction
		let CleanURL = RequestURL.split("?")[0];
		let Fragments = CleanURL.split("/");

		// The URL variable tells us the current path of the virtual browser
		this.URL = RequestURL;

		// The App variable tells us what app we're currently in
		this.App = Fragments[0];

		// The Page variable tells us what page we're currently in
		this.Page = Fragments[1] || "index";

		// Update the browser URL without refreshing the page
		window.top.history.replaceState(this.Page, false, this.URL);

		// Determine if the user is requesting the home page
		let IsHome = this.App === "home";

		// Let the DOM know if we're on the home page
		document.querySelector("body").setAttribute("data-home", IsHome);

		// Update theme for the new app
		this.UpdateThemeForApp();
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Navigation Handler
	 ** ==================================
	 */

	Navigate(RequestURL, NewTab = false) {
		// Check if there is a navigation warning in place
		// If this is the case we warn the user before leaving the page
		if (this.NavigationWarningParams && this.NavigationWarningParams?.State === true) {
			if (confirm(this.NavigationWarningParams.Message)) {
				this.NavigationWarning(false);
			} else {
				return false;
			}
		}

		// Check if this url is to an external site
		let External = (RequestURL.includes("http") && !RequestURL.includes(TL.Location.Domain)) || RequestURL.includes(".com");

		// If the call manually requests to open the page in a new tab or the url is to an external site
		if (NewTab === true || External) {
			RequestURL = External ? RequestURL : TL.Location.DomainBase + "/" + RequestURL;

			// Open a new tab
			window.open(RequestURL, "_blank");

			// Stop execution here
			return true;
		}

		// Clear previously imported templates
		//TL.Templates = $($.parseHTML('<div id="TL-Templates"></div>'));

		// Start the page transition animation
		this.PageTransition(true);

		// Start the loading animation
		this.Loading(true);

		// If no URL was requested, default to the home page
		if (!RequestURL) {
			// If a user is logged in, take them to the home page, otherwise take them to login
			RequestURL = TL.User ? TL.SiteConfig["Home URL"] : "login/";
		}

		// Clear the developer console
		console.clear();

		// Log the navigation request in the console
		TL.DebugLog("Navigate", RequestURL, "#FFBD00");

		// Set location will update the virtual browsers global params and update the browsers URL
		this.SetLocation(RequestURL);

		// Redirect the virtual browser to the requested URL
		window.top.frames["TL-Browser"].location = TL.Location.DomainBase + "/lib/browser?url=" + RequestURL;

		// Construct the apps navigation pages
		this.ConstructNavigation();

		if (this.App === "home") {
			// End the page transition animation & loading animation
			TL.Browser.Loading(false);
			TL.Browser.PageTransition(false);
		}
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Refresh Page
	 ** ==================================
	 */

	Refresh(Soft = false) {
		if (Soft === true) {
			TL.Browser.Navigate(TL.Browser.URL);

			return true;
		}

		this.Window.location.reload();
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Page Transition Animation
	 ** ==================================
	 */

	PageTransition(State) {
		let AppIconElement = $("#TL-System-Nav > .active-app");

		if (State) {
			this.TransitionState = true;

			$("#TL-Browser").addClass("start-page-transition");

			AppIconElement.removeClass("active");

			setTimeout(function () {
				if (!TL.Browser.TransitionState) {
					return false;
				}

				$("#TL-Browser").addClass("end-page-transition");
			}, 150);
		} else {
			this.TransitionState = false;

			// Fade out initial loading screen
			$("#TL-Browser-Initializing")
				.addClass("animate-out")
				.fadeOut(300, function () {
					$(this).remove();
				});

			$("#TL-Browser").removeClass("start-page-transition end-page-transition");

			AppIconElement.toggleClass("active", this.App != "home");

			let AppIconPath = "native/resources/icons/apps/" + this.App + "/app.png";
			let AppInfo = TL["Apps"][this.App];
			let AppDisplayName = AppInfo["Display Name"];

			AppIconElement.children("img").attr("src", AppIconPath);
			$("#TL-Primary-Favicon").attr("href", AppIconPath);
			$("title").text(AppDisplayName);
		}
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Browsers Loading Animation
	 ** ==================================
	 */

	Loading(State) {
		let BodyElement = document.querySelector("body");

		if (State === true) {
			BodyElement.setAttribute("data-loading", "true");
		} else {
			BodyElement.setAttribute("data-loading", "end");

			setTimeout(function () {
				BodyElement.setAttribute("data-loading", "false");
			}, 380);
		}
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Construct Home Screen Apps
	 ** ==================================
	 */

	ConstructApps() {
		let Wrapper = $("#TL-Home .apps");

		let Template = TL.Template("TL-App");

		Object.values(TL.Apps).forEach(function (App) {
			if (App.Visibility != "1") {
				return true;
			}

			// If a custom home page was specified, use that
			let HomePage = App["Default Page"] && App["Default Page"] != null ? App["Default Page"] : "";

			// Construct the link
			// Some apps can serve as external links to other sites
			App.Link = App["External Link"] ? App["External Link"] : App.App + "/" + HomePage;

			let FilledTemplate = TL.FillTemplate(Template, App);

			Wrapper.append(FilledTemplate);
		});

		// Append the inventory status element on the app
		let InventoryAppElement = $('.TL-App[data-app="inventory"]');
		if (InventoryAppElement.length > 0) {
			let StatusElement = `<h2 class="inventory-status" status="` + TL["Inventory Status"] + `">` + TL["Inventory Status"] + `</h2>`;
			InventoryAppElement.find(".text-body").append(StatusElement);
		}

		return true;
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Construct Page Navigation
	 ** ==================================
	 */

	ConstructNavigation() {
		let Wrapper = $("#TL-App-Nav > .page-options").html("");

		let Template = TL.Template("TL-Nav-Page");

		let Pages = Object.values(TL?.Apps?.[TL.Browser.App]?.Pages || {});

		let VisiblePages = 0;

		Pages.forEach(function (Page) {
			if (Page.Visibility != "1") {
				return true;
			}

			VisiblePages++;

			Page.Link = Page.App + "/" + Page.Page;

			Page.PageIcon = Page.Icon ? Page.Icon : Page.Page === "index" ? "home" : "document";

			let FilledTemplate = TL.FillTemplate(Template, Page);

			Wrapper.append(FilledTemplate);
		});

		let ShowNav = Boolean(VisiblePages < 2);

		$("#TL-App-Nav").toggleClass("hidden", ShowNav);
		$("#TL-Browser").toggleClass("app-nav-hidden", ShowNav);

		Wrapper.find('.page-option[data-page="' + TL.Browser.Page + '"]').addClass("selected");

		if (TL.User) {
			$("nav [user-photo]").attr("user-photo", TL.User.Info["User ID"]);
		} else {
			$("#TL-Browser").addClass("guest-nav-visible");
		}

		TL.InitializeUserPhotos("nav");

		// Show/hide theme toggle based on app dark mode support
		const AppInfo = TL["Apps"] && TL["Apps"][this.App] ? TL["Apps"][this.App] : null;
		const DarkModeEnabled = AppInfo && AppInfo["Dark Mode Enabled"] === 1;
		$(".TL-System-Theme-Toggle").toggle(DarkModeEnabled);

		// Hide Nav for pharmacy users
		if (TL.SiteConfig?.["Hide Nav"]) {
			$("#TL-System-Nav").hide();
			$("#TL-Mobile-Nav").hide();

			$("#TL-Browser").css({ width: "100%", height: "100%", left: "0" });
		}
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Display Dashboard Messages
	 ** ==================================
	 */

	DashboardMessages() {
		// Dev Note: Disabled
		return true;

		if (TL.User.Messages) {
			let MessageElements = ``;
			let Template = TL.Template("TL-Message-Preview-Home");

			TL.User.Messages.forEach(function (MessageData) {
				if (MessageData["Read Status"] === true || MessageData["Important"] !== true) {
					return true;
				}
				MessageElements += TL.FillTemplate(Template, MessageData);
			});

			TL.ContentWindow.Append({
				Heading: "Messages",
				Content: MessageElements,
				Size: "medium",
			});
		}
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Construct Daily Summary
	 ** ==================================
	 */

	RefreshDailySummary() {
		if (!TL.User) {
			return false;
		}

		$(".daily-summary-wrapper .populate-first-name").text(TL.User.Info["First Name"]);

		let ResultCount = 0;
		let Wrapper = $(".daily-summary-wrapper .population").html("");
		let Templates = {
			Message: TL.Template("TL-Message-Preview-Home"),
			Task: TL.Template("TL-Task-Daily-Summary"),
		};

		if (TL.User.Messages) {
			TL.User.Messages.forEach(function (MessageData) {
				if (MessageData["Read Status"] === true) {
					return true;
				}
				let FilledTemplate = TL.FillTemplate(Templates.Message, MessageData);
				Wrapper.append(FilledTemplate);
				ResultCount++;
			});
		}

		// Set notification count for messages option  (This only includes the messages portion)
		$(".TL-DATA-Message-Count").attr("count", ResultCount).text(ResultCount);

		// Add tasks notification to daily summary
		let TaskCount = parseInt($(".TL-DATA-Task-Count").text());

		if (TL.User.Tasks && TaskCount) {
			let TaskData = {
				Heading: TaskCount === 1 ? 'You have <span class="task-count">1</span> task' : 'You have <span class="task-count">' + TaskCount + "</span> tasks",
				Body: "You have pending tasks to complete, click this card to open your tasks board",
			};

			let FilledTemplate = TL.FillTemplate(Templates.Task, TaskData);
			Wrapper.prepend(FilledTemplate);
			ResultCount++;
		}

		if (ResultCount === 0) {
			$(".daily-summary-wrapper").addClass("zero-results");
		} else {
			$(".daily-summary-wrapper").removeClass("zero-results");
		}

		TL.InitializeUserPhotos(".daily-summary-wrapper");
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Pin Page
	 ** ==================================
	 */

	PinApp(Link = false) {
		if (!Link) {
			Link = this.URL;
		}

		let InitialDelay = 1;

		let Wrapper = $("#TL-Pinned-App-Wrapper");

		if (Wrapper.children().length > 0) {
			this.UnpinApp();

			InitialDelay = 400;
		}

		setTimeout(function () {
			let LinkParts = Link.split("/");

			let AppName = LinkParts[0];

			let Template = TL.Template("TL-Pinned-App");

			let FilledTemplate = TL.FillTemplate(Template, { App: AppName, Link: Link });

			Wrapper.append(FilledTemplate);

			Wrapper.addClass("animate-1");

			setTimeout(function () {
				Wrapper.addClass("active");
				setTimeout(function () {
					Wrapper.addClass("animate-2");
					setTimeout(function () {
						Wrapper.addClass("animate-3").removeClass("animate-1 animate-2");
					}, 500);
				}, 100);
			}, 200);
		}, InitialDelay);
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Pin Page
	 ** ==================================
	 */

	UnpinApp() {
		let Wrapper = $("#TL-Pinned-App-Wrapper");

		Wrapper.removeClass("animate-1 animate-2 animate-3");

		setTimeout(function () {
			Wrapper.removeClass("active").html("");
		}, 400);
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Edit URL Without Refresh
	 ** ==================================
	 */

	EditURL(NewURL = false) {
		window.top.history.pushState("", "", NewURL);
		this.URL = NewURL;
		return true;
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Toggle Navigation Warning
	 ** ==================================
	 */

	NavigationWarning(State, Message = false) {
		this.NavigationWarningParams = {};
		this.NavigationWarningParams.State = State;
		this.NavigationWarningParams.Message = Message || "Are you sure? There may be unsaved changes";
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Refresh Tasks Board
	 ** ==================================
	 */

	RefreshTasks() {
		if (!TL.User) {
			return false;
		}

		// Prevent if tasks are already trying to load
		// OR if tasks is being viewed by user
		if ($("#TL-Tasks").find(".TL-Loading").length > 0 || $(".TL-Task-Expanded").length > 0) {
			return false;
		}

		let WrapperElement = $("#TL-Tasks").removeClass("zero-results");
		let DateParams = TL.DateTime.Info();

		// Set the current day in the DOM
		WrapperElement.find(".date > .day").text(DateParams.day);
		WrapperElement.find(".date > .weekday").text(DateParams.weekday);
		WrapperElement.find(".date > .month-year").text(DateParams.month + ", " + DateParams.year);

		// Set the count in the nav
		$(".TL-DATA-Task-Count").attr("count", 0).text(0);

		const CardsWrapper = WrapperElement.find(".population").html("");

		TL.Loading.Start("#TL-Tasks > .board");
		TL.Loading.Start("nav .option.TL-Expand-Tasks");

		TL.Agent({
			agent: ["sys", "tasks"],
			data: {},
			success: function (Tasks) {
				TL.User.Tasks = Tasks;
				let TotalTaskCount = 0;

				// Reverse the object so that SCRIPT type shows first in the tasks list
				Tasks = Object.keys(Tasks)
					.reverse()
					.reduce((obj, key) => {
						obj[key] = Tasks[key];
						return obj;
					}, {});

				// Iterate through the task types
				Object.keys(Tasks).forEach(function (TaskType) {
					// Retrieve the card template for this specific task type
					const CardTemplate = TL.Template("TL-Task-Card-" + TaskType);
					const ChipTemplate = TL.Template("TL-Count-Chip");
					let CountChips = ``;

					// Error check the card template
					if (!CardTemplate) {
						TL.DebugLog("Tasks Error", "Failed to load task card template for type: " + TaskType);
						return true;
					}

					// Iterate through each of the tasks within this task category / type
					Tasks[TaskType].forEach(function (TaskInfo) {
						TotalTaskCount++;

						// Reset the chips for each task
						CountChips = ``;

						// If Count is an object, that means there are sub-counts.
						if (typeof TaskInfo["Count"] === "object") {
							// We are going to add the sub-counts together to get the total
							TaskInfo["Total"] = Object.values(TaskInfo["Count"]).reduce((a, b) => a + b, 0);

							// Build a count chip for every count item for this task type
							// This applies to tasks that have specific sub-counts - such as birthdays/anniversaries
							Object.keys(TaskInfo["Count"]).forEach(function (CountItem) {
								CountChips += TL.FillTemplate(ChipTemplate, { Label: CountItem, Count: TaskInfo["Count"][CountItem], Theme: TaskInfo["Theme"] });
							});
							TaskInfo["CountChips"] = CountChips;
						} else {
							// Otherwise, Count is just an integer that represents the total, so use that
							TaskInfo["Total"] = TaskInfo["Count"];
						}

						const FilledCardTemplate = TL.FillTemplate(CardTemplate, TaskInfo);
						CardsWrapper.append(FilledCardTemplate);
					});
				});

				if (TotalTaskCount < 1) {
					WrapperElement.addClass("zero-results");
				}

				// Set the count in the nav
				$(".TL-DATA-Task-Count").attr("count", TotalTaskCount).text(TotalTaskCount);

				// Refresh the daily summary to include your tasks
				TL.Browser.RefreshDailySummary();

				TL.Loading.Stop("#TL-Tasks > .board");
				TL.Loading.Stop("nav .option.TL-Expand-Tasks");
			},
			error(error) {
				TL.DebugLog("Tasks Failed To Load", error);

				WrapperElement.addClass("zero-results");

				TL.Loading.Stop("#TL-Tasks > .board");
				TL.Loading.Stop("nav .option.TL-Expand-Tasks");
			},
		});
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Initialize Ping Service
	 ** ==================================
	 */

	InitializePing(Interval) {
		// Ensure that this only executes at the browser level
		if (this.NavigationLevel !== "browser") {
			return false;
		}

		// Ensure the user is logged in
		if (!TL.User) {
			return false;
		}
		// Set backup incase interval comes over as NaN
		if (!Interval) {
			Interval = 60;
		}
		// Convert the seconds to milliseconds
		Interval = Interval * 1000;
		// Log what's happening
		TL.DebugLog("Initialize Ping", Interval + " Interval");
		// Execute the ping on every interval
		setInterval(function () {
			TL.Browser.ExecutePing();
		}, Interval);
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Execute Ping Service
	 ** ==================================
	 */

	ExecutePing() {
		TL.Agent({
			agent: ["sys", "ping"],
			data: {},
			success: function (Result) {
				TL.DebugLog("Ping", Result);

				// Auto logout user if session expired
				if (!Result.User) {
					TL.Browser.Navigate("login/logout");
				}

				// Establish the user object globally
				TL["User"] = Result["User"];

				// Establish the users object globally
				TL["Users"] = Result["Users"];

				// Establish the stores object globally
				// TL["Stores"] = Result["Stores"];

				// Refresh user's tasks
				TL.Browser.RefreshTasks();
			},
			error(error) {
				TL.DebugLog("Ping Error", error);
			},
		});
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Expand System Search
	 ** ==================================
	 */

	ExpandSystemSearch() {
		$("#TL-Search").addClass("active").find(".search-bar > input").focus();
		//TL.Browser.SystemSearch("active: ");
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Run System Search
	 ** ==================================
	 */

	SystemSearch(Query) {
		let CategoryWrapper = $("#TL-Search > .results").html("");

		let Results = TL.Search.All(Query);
		let ZeroResults = true;
		let Templates = {};

		Templates.Category = TL.Template("TL-Search-Results-Category");
		Templates.Users = TL.Template("TL-User-Search-Result");
		Templates.Stores = TL.Template("TL-Store-Search-Result");
		Templates.Apps = TL.Template("TL-App-Search-Result");
		Templates.Files = TL.Template("TL-File-Search-Result");

		Object.keys(Results).forEach(function (ResultCategory) {
			let ResultSet = Results[ResultCategory];

			if (ResultSet.length > 0) {
				ZeroResults = false;

				let FilledResults = ``;

				ResultSet.forEach(function (ResultData) {
					FilledResults += TL.FillTemplate(Templates[ResultCategory], ResultData);
				});

				let CategoryData = { Category: ResultCategory, Results: FilledResults };

				let FilledCategory = TL.FillTemplate(Templates.Category, CategoryData);

				CategoryWrapper.append(FilledCategory);
			}
		});

		$("#TL-Search").removeClass("initial-message").toggleClass("zero-results", ZeroResults);

		TL.InitializeUserPhotos("#TL-Search");
		TL.InitializeStorePhotos("#TL-Search");
	}
	/*
	 **
	 **
	 **
	 **
	 **
	 ** Bind User Data
	 ** ==================================
	 */

	BindUserData() {
		Object.values(TL.Stores).forEach(function (StoreInfo) {
			const StoreEmployeeIDs = StoreInfo.Employees;
			StoreEmployeeIDs.forEach(function (StoreEmployeeID) {
				if (typeof TL.Users[StoreEmployeeID] != "object") {
					return true;
				}
				TL.Users[StoreEmployeeID]["Store ID"] = StoreInfo["Store ID"];
				TL.Users[StoreEmployeeID]["Store Name"] = "#" + StoreInfo["Store ID"] + " - " + StoreInfo["City"];
			});
		});
	}

	/*
	 **
	 **
	 **
	 **
	 **
	 ** Theme Management System
	 ** ==================================
	 */

	// Initialize the theme system
	InitializeTheme() {
		const SavedTheme = localStorage.getItem(this.ThemeKey);
		const SystemPrefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches;
		let IsDarkMode = SavedTheme === "dark" || (SavedTheme === null && SystemPrefersDark);

		// Check if current app has dark mode enabled
		const AppInfo = TL["Apps"] && TL["Apps"][this.App] ? TL["Apps"][this.App] : null;
		const DarkModeEnabled = AppInfo && AppInfo["Dark Mode Enabled"] === 1;

		// Force light mode if app doesn't support dark mode
		if (!DarkModeEnabled) {
			IsDarkMode = false;
		}

		this.SetTheme(IsDarkMode ? "dark" : "light");

		// Listen for system preference changes
		window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change", (Event) => {
			// Only auto-switch if user hasn't set a preference and app supports dark mode
			if (!localStorage.getItem(this.ThemeKey) && DarkModeEnabled) {
				this.SetTheme(Event.matches ? "dark" : "light");
			}
		});
	}

	// Set the current theme
	SetTheme(Theme) {
		// Set the theme attribute on the body element
		document.body.setAttribute("data-theme", Theme);

		// Check if current app has dark mode enabled
		const AppInfo = TL["Apps"] && TL["Apps"][this.App] ? TL["Apps"][this.App] : null;
		const DarkModeEnabled = AppInfo && AppInfo["Dark Mode Enabled"] === 1;

		// Apply dark mode only if enabled for the app and theme is dark
		const ShouldApplyDark = Theme === "dark" && DarkModeEnabled;

		$("#TL-App-Nav").toggleClass("dark", ShouldApplyDark);
		$("#TL-Home").toggleClass("dark", ShouldApplyDark);
		$("#TL-Browser").toggleClass("dark", ShouldApplyDark);

		// Store preference for both system and current app
		localStorage.setItem(this.ThemeKey, Theme);
		if (this.App) {
			localStorage.setItem(`${this.App}-theme`, Theme);
		}

		// Dispatch event for apps to listen to
		window.dispatchEvent(
			new CustomEvent("ThemeChanged", {
				detail: { Theme: Theme, Timestamp: Date.now() },
			})
		);

		// Update app window if available
		if (this.AppWindow) {
			this.AppWindow.body.setAttribute("data-theme", Theme);
			this.AppWindow.dispatchEvent(
				new CustomEvent("ThemeChanged", {
					detail: { Theme: Theme, Timestamp: Date.now() },
				})
			);
		}

		TL.DebugLog("Theme Changed", Theme);
	}

	// Toggle the current theme
	ToggleTheme() {
		// Check if current app has dark mode enabled
		const AppInfo = TL["Apps"] && TL["Apps"][this.App] ? TL["Apps"][this.App] : null;
		const DarkModeEnabled = AppInfo && AppInfo["Dark Mode Enabled"] === 1;

		// Only allow toggle if app supports dark mode
		if (!DarkModeEnabled) {
			this.SetTheme("light");
			return "light";
		}

		const CurrentTheme = document.body.getAttribute("data-theme");
		const NewTheme = CurrentTheme === "dark" ? "light" : "dark";
		this.SetTheme(NewTheme);
		return NewTheme;
	}

	// Get the current theme
	GetCurrentTheme() {
		return document.body.getAttribute("data-theme") || "light";
	}

	// Update the theme for the current app
	UpdateThemeForApp() {
		const AppInfo = TL["Apps"] && TL["Apps"][this.App] ? TL["Apps"][this.App] : null;
		const DarkModeEnabled = AppInfo && AppInfo["Dark Mode Enabled"] === 1;

		// Show/hide theme toggle based on dark mode support
		$(".TL-System-Theme-Toggle").toggle(DarkModeEnabled);

		// Get app-specific theme preference
		const AppTheme = localStorage.getItem(`${this.App}-theme`);

		// If app doesn't support dark mode, force light theme
		if (!DarkModeEnabled) {
			this.SetTheme("light");
		} else {
			// Use app-specific theme if available, otherwise use current theme
			const ThemeToUse = AppTheme || this.GetCurrentTheme();
			this.SetTheme(ThemeToUse);
		}
	}

	// Initialize dark mode
	InitializeDarkMode() {
		this.InitializeTheme();
	}
}

/*
 **
 **
 **
 **
 **
 ** Event Listeners
 ** ==================================
 */

$(document).on("keyup", "#TL-Search .search-bar > input", function (EV) {
	let Query = this.value;

	$("#TL-Search > .results").html("");

	TL.PreventRepetition(
		"TL-Search",
		function () {
			TL.Browser.SystemSearch(Query);
		},
		300
	);
});

$(document).on("click", "#TL-Search", function (EV) {
	if ($(EV.target).closest(".search-bar").length > 0) {
		return false;
	}

	$("#TL-Search").removeClass("active zero-results").addClass("initial-message").find(".results").html("").end().find(".search-bar > input").val("");
});

$(document).on("click", "#TL-Home.expand-daily-summary .daily-summary-wrapper", function (EV) {
	if ($(EV.target).closest(".TL-Notification-Preview").length > 0) {
		return false;
	}
	$("#TL-Home").removeClass("expand-daily-summary");
});

$(document).on("click", "#TL-Home .TL-Notification-Preview", function (EV) {
	let MessageID = $(this).attr("data-message-id");

	TL.Browser.Navigate("messages/?message=" + MessageID);
});

$(document).on("click", "#TL-System-Nav > .secondary-options-toggle:not(.expanded)", function (EV) {
	$(this).addClass("expanded");
});

$(document).on("click", "#TL-System-Nav > .secondary-options-toggle.expanded > .secondary-action", function (EV) {
	$(this).parent().removeClass("expanded");

	let Action = $(this).attr("data-action");

	switch (Action) {
		case "share":
			TL.CopyToClipboard(window.location.href);
			TL.Notify.Banner("Link copied", "The link to this page has been copied to your clipboard");
			break;
		case "help":
			TL.Browser.Navigate("https://teamlyders.co/tickets/create-ticket");
			break;
		case "sign-out":
			TL.Browser.Navigate("login/logout");
			break;
		default:
			return false;
	}
});

$(document).on("click", ".TL-Message-Preview-Home", function () {
	if ($(this).hasClass("TL-Task-Daily-Summary")) {
		return false;
	}
	let MessageID = this.getAttribute("message-id");
	TL.Browser.Navigate("messages/?message=" + MessageID);
});

$(document).on("click", "a", function (e) {
	e.preventDefault();
	TL.Browser.Navigate(this.getAttribute("href"));
	return false;
});

// Expand To Do Board
$(document).on("click", ".TL-Expand-Tasks", function () {
	$("#TL-Tasks").fadeIn(200, function () {
		$("body").addClass("TL-Task-Expanded", "true");
	});
});

// Collapse To Do Board
$(document).on("click", "button.TL-Collapse-Tasks, #TL-Tasks", function (e) {
	if ($(this).closest("#TL-Tasks").length > 0) {
		if ($(e.target).closest(".board").length > 0 && !$(this).hasClass("TL-Collapse-Tasks")) {
			return false;
		}
	}
	$("body").removeClass("TL-Task-Expanded");
	setTimeout(function () {
		$("#TL-Tasks").fadeOut(200);
	}, 300);
});

// Click On A Task
$(document).on("click", "#TL-Tasks .TL-Task-Card", function (e) {
	let Link = $(this).attr("link");
	if (!Link) {
		TL.Notify.Banner("Oops!", "This task does not have a link attached to it");
		return false;
	}
	TL.Browser.Navigate(Link);
	$("button.TL-Collapse-Tasks").click();
});

// Theme toggle functionality
$(document).on("click", ".TL-System-Theme-Toggle", function () {
	TL.Browser.ToggleTheme();
});

// Global theme access
TL.Ready(function () {
	TL.Browser = new Browser();
});
