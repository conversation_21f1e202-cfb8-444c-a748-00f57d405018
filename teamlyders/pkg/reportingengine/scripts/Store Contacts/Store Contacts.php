<?php
$MyStores = ($this->TL->User->VerifyPermissions(['DOO', 'MC']) === true) ? $this->TL->Stores->Retrieve() : $this->TL->User->Stores();

$Report = $RelativeUserIDs = [];

// Iterate through each of the users stores
foreach ($MyStores as $StoreID => $StoreDetail) {
    foreach ($StoreDetail['Assignments'] as $AssignmentKey => $AssignmentDetail) {
        $RelativeUserIDs[] = $AssignmentDetail['Occupant'] ?? 10;
    }
    foreach ($StoreDetail['Hierarchy'] as $HierarchyKey => $HierarchyDetail) {
        $RelativeUserIDs[] = $HierarchyDetail['User ID'] ?? 10;
    }
}

$RelativeUserIDs = array_unique($RelativeUserIDs);

$RelativeUsersDetail = $this->TL->User->SafeInfo($RelativeUserIDs);

// We grab this view for the assignment info 
$ExtraStoreInfoMaster = $this->TL->Database->Query("SELECT * FROM `vw_StoreAlignments`");
foreach ($ExtraStoreInfoMaster as $StoreDetail) {
    $StoreID = $StoreDetail['Store ID'];
    if (isset($MyStores[$StoreID])) {
        $MyStores[$StoreID]['RGM'] = $StoreDetail['RGM Name'];
        $MyStores[$StoreID]['Payroll'] = $StoreDetail['Payroll Name'];
        $MyStores[$StoreID]['HR'] = $StoreDetail['HR Name'];
        $MyStores[$StoreID]['Maintenance'] = $StoreDetail['Maintenance Name'];
        $MyStores[$StoreID]['Field IT'] = $StoreDetail['Tech Name'];
        $MyStores[$StoreID]['Security'] = $StoreDetail['Security Name'];
    }
}

// Needed for Arby's store to be added to the end of the report
$Arbys = [];

// Iterate through each of the users stores
foreach ($MyStores as $StoreID => $StoreDetail) {

    $ACID = $StoreDetail['Hierarchy']['area']['User ID'];
    $MCID = $StoreDetail['Hierarchy']['market']['User ID'];
    $DOID = $StoreDetail['Hierarchy']['region']['User ID'];

    // Get the store entry
    $Entry = [
        'Store ID' => $StoreID,
        'Store Address' => $StoreDetail['Street Address'] . " " . $StoreDetail['City'] . ", " . $StoreDetail['State'] . " " . $StoreDetail['Zip Code'],
        'Store Phone' => $this->TL->PrettyPhone($StoreDetail['Phone Number']),
        'General Manager' => $StoreDetail['RGM'] ?? 'Unknown',
        'General Manager Email' => 'rs' . str_pad($StoreID, 6, '0', STR_PAD_LEFT) . '@tacobell.com',
        'Area Coach' => $RelativeUsersDetail[$ACID]['Full Name'] ?? 'Unknown',
        'Area Coach Email' => $RelativeUsersDetail[$ACID]['Work Email'] ?? 'Unknown',
        'Market Coach' => $RelativeUsersDetail[$MCID]['Full Name'] ?? 'Unknown',
        'Market Coach Email' => $RelativeUsersDetail[$MCID]['Work Email'] ?? 'Unknown',
        'Director of Operations' => $RelativeUsersDetail[$DOID]['Full Name'] ?? 'Unknown',
        'Director of Operations Email' => $RelativeUsersDetail[$DOID]['Work Email'] ?? 'Unknown',
        'Payroll' => $StoreDetail['Payroll'] ?? 'Unknown',
        'Human Resources' => $StoreDetail['HR'] ?? 'Unknown',
        'Maintenance' => $StoreDetail['Maintenance'] ?? 'Unknown',
        'Field IT' => $StoreDetail['Tech'] ?? 'Unknown',
        'Security' => $StoreDetail['Security'] ?? 'Unknown',
    ];

    // If Arby's, store in it's own object to append the to end
    if ($StoreDetail['Brand'] == 'RB-US') {
        $Arbys[] = $Entry;
    }

    // TB, add to the report
    else {
        $Report[] = $Entry;
    }
}

// Merge arby's to the end of the report
$Report = array_merge($Report, $Arbys);

// Send back data
$this->TL->Agent->Exit($Report);
