<?php
// Select all users with status of Active
$Query = "  SELECT  u.`User ID`, u.`First Name`, u.`Last Name`,
                    u.`Email`, u.`Phone`, u.`Status`
            FROM    `Users` u WHERE u.`STATUS` = 'Active'";

// Execute the query and check for results
if (!$ActiveEmployees = $this->TL->Database->Query($Query)) {
    $this->TL->Agent->Error('Could not retrieve employees');
}

// User IDs to exclude from the report
$UserIDsToExclude = \Config\System::AvibraReportExcludedUserIDs;

// Filter out employees whose User IDs are in the exclusion list
$FilteredEmployees = array_values(array_filter(
    $ActiveEmployees,
    fn($Employee) => !in_array($Employee['User ID'], $UserIDsToExclude)
));

// Remove 'User ID' field from each employee record and create final results array
$Results = array_map(
    fn($Employee) => array_diff_key($Employee, array_flip(['User ID'])),
    $FilteredEmployees
);

// Send back results
$this->TL->Agent->Exit($Results);
