<?php
$MyStores = ($this->TL->User->VerifyPermissions(['SECURITY', 'DOO']) === true || $this->TL->User->ID() == 84) ? $this->TL->Stores->Retrieve() : $this->TL->User->Stores();

$Report = $RelativeUserIDs = [];

// Iterate through each of the users stores
foreach ($MyStores as $StoreID => $StoreDetail) {
    foreach ($StoreDetail['Assignments'] as $AssignmentKey => $AssignmentDetail) {
        $RelativeUserIDs[] = $AssignmentDetail['Occupant'] ?? 10;
    }
    foreach ($StoreDetail['Hierarchy'] as $HierarchyKey => $HierarchyDetail) {
        $RelativeUserIDs[] = $HierarchyDetail['User ID'] ?? 10;
    }
}

$RelativeUserIDs = array_unique($RelativeUserIDs);

$RelativeUsersDetail = $this->TL->User->SafeInfo($RelativeUserIDs);

// We grab this view for the assignment info 
$ExtraStoreInfoMaster = $this->TL->Database->Query("SELECT * FROM `vw_StoreAlignments`");
foreach ($ExtraStoreInfoMaster as $StoreDetail) {
    $StoreID = $StoreDetail['Store ID'];
    if (isset($MyStores[$StoreID])) {
        $MyStores[$StoreID]['RGM Name'] = $StoreDetail['RGM Name'];
        $MyStores[$StoreID]['Payroll Name'] = $StoreDetail['Payroll Name'];
        $MyStores[$StoreID]['HR Name'] = $StoreDetail['HR Name'];
        $MyStores[$StoreID]['Maintenance Name'] = $StoreDetail['Maintenance Name'];
        $MyStores[$StoreID]['Tech Name'] = $StoreDetail['Tech Name'];
        $MyStores[$StoreID]['Security Name'] = $StoreDetail['Security Name'];
    }
}

// Needed for Arby's store to be added to the end of the report
$Arbys = [];

// Iterate through each of the users stores
foreach ($MyStores as $StoreID => $StoreDetail) {

    $ACID = $StoreDetail['Hierarchy']['area']['User ID'];
    $MCID = $StoreDetail['Hierarchy']['market']['User ID'];
    $DOID = $StoreDetail['Hierarchy']['region']['User ID'];

    // Get the store entry
    $Entry = [
        'Store ID' => $StoreID,
        'Brand' => $StoreDetail['Brand'],
        'Entity' => $StoreDetail['Payroll Company'],
        'Address' => $StoreDetail['Street Address'],
        'City' => $StoreDetail['City'],
        'State' => $StoreDetail['State'],
        'Zip Code' => $StoreDetail['Zip Code'],
        'Construction Status' => $StoreDetail['Construction Status'],
        'Phone' => $this->TL->PrettyPhone($StoreDetail['Phone Number']),
        'Employee Count' => count($StoreDetail['Employees']),
        'Opening Date' => 'Coming Soon!',
        'Performance Braket' => 'Coming Soon!',
        'General Manager' => $StoreDetail['RGM Name'] ?? 'Unknown',
        'Area Coach' => $RelativeUsersDetail[$ACID]['Full Name'] ?? 'Unknown',
        'Market Coach' => $RelativeUsersDetail[$MCID]['Full Name'] ?? 'Unknown',
        'Director of Operations' => $RelativeUsersDetail[$DOID]['Full Name'] ?? 'Unknown',
        'Payroll' => $StoreDetail['Payroll Name'] ?? 'Unknown',
        'Human Resources' => $StoreDetail['HR Name'] ?? 'Unknown',
        'Maintenance' => $StoreDetail['Maintenance Name'] ?? 'Unknown',
        'Field IT' => $StoreDetail['Tech Name'] ?? 'Unknown',
        'Security' => $StoreDetail['Security Name'] ?? 'Unknown',
    ];

    // If Arby's, store in it's own object to append the to end
    if ($StoreDetail['Brand'] == 'RB-US') {
        $Arbys[] = $Entry;
    }

    // TB, add to the report
    else {
        $Report[] = $Entry;
    }
}

// Merge arby's to the end of the report
$Report = array_merge($Report, $Arbys);

// Send back data
$this->TL->Agent->Exit($Report);
