<?php
$this->TL->Unpack(['System' => ['DPoll']]);

$ShowStoppers = $this->TL->DPoll->GetShowStoppers();


$MyStores = $this->TL->User->Stores();

$TodayDate = $this->TL->GetDate();

$Report = $RelativeUserIDs = $ExtraStoreInfo = [];

// Iterate through each of the users stores
foreach ($MyStores as $StoreID => $StoreDetail) {
    foreach ($StoreDetail['Hierarchy'] as $HierarchyKey => $HierarchyDetail) {
        $RelativeUserIDs[] = $HierarchyDetail['User ID'] ?? 10;
    }
}

// We grab this view for the assignment info 
$ExtraStoreInfoMaster = $this->TL->Database->Query("SELECT * FROM `vw_StoreAlignments`");
foreach ($ExtraStoreInfoMaster as $StoreDetail) {
    $StoreID = $StoreDetail['Store ID'];
    if (isset($MyStores[$StoreID])) {
        $MyStores[$StoreID]['RGM Full Name'] = $StoreDetail['RGM Name'];
        $MyStores[$StoreID]['RGM User ID'] = $StoreDetail['RGM User ID'];
    }
}

$RelativeUserIDs = array_unique($RelativeUserIDs);

$RelativeUsersDetail = $this->TL->User->SafeInfo($RelativeUserIDs);

// Iterate through each of the users stores
foreach ($MyStores as $StoreID => $StoreDetail) {

    // Only include stores that are on showstoppers report
    if (empty($ShowStoppers[$StoreID]) || $StoreDetail['Construction Status'] != 'OPEN') {
        continue;
    }

    $ShowStopperDetail = $ShowStoppers[$StoreID];

    $ACID = $StoreDetail['Hierarchy']['area']['User ID'];
    $MCID = $StoreDetail['Hierarchy']['market']['User ID'];
    $DOID = $StoreDetail['Hierarchy']['region']['User ID'];

    $Report[] = [
        'Store ID' => $StoreID,
        'Consecutive Days' => $ShowStopperDetail['Consecutive Days'],
        'Showstoppers In Last 30 Days' => $ShowStopperDetail['Past 30 Days'],
        'Days Missing' => $ShowStopperDetail['Missing'],
        'Brand' => $StoreDetail['Brand'],
        'City' => $StoreDetail['City'],
        'State' => $StoreDetail['State'],
        'General Manager' => $StoreDetail['RGM Full Name'] ?? 'Unknown',
        'Area Coach' => $RelativeUsersDetail[$ACID]['Full Name'] ?? 'Unknown',
        'Market Coach' => $RelativeUsersDetail[$MCID]['Full Name'] ?? 'Unknown',
        'Director of Operations' => $RelativeUsersDetail[$DOID]['Full Name'] ?? 'Unknown',
        'Report Date' => $TodayDate
    ];
}

usort($Report, function ($a, $b) {
    return $a['Store ID'] <=> $b['Store ID'];
});

$this->TL->Agent->Exit($Report);
