<?php

namespace ReportingEngine;

trait  Utils
{

    function MyStores(array $Params = [], $OutputKey = 'MyStores'): array
    {
        // $Params[$OutputKey] = [867, 1264, 1439, 1646, 1651, 1661]; //DEV NOTE:  DELETE ME
        // return $Params;

        // USE TL SYSTEM FUNCTIONS
        $Params[$OutputKey] = $this->TL->User->Stores(false, true);
        return $Params;
    }

    function MyUsers(array $Params = [], $OutputKey = 'MyUsers'): array
    {
        // $Users = [226053, 91724, 400896, 34961, 253293, 246592];
        // $Params[$OutputKey] = $Users; //DEV NOTE:  DELETE ME
        // return $Params;


        // USE TL SYSTEM FUNCTIONS
        $Params[$OutputKey] = $this->TL->User->Employees(false, true);
        return $Params;
    }
}
