<?php

namespace ReportingEngine;

require_once 'utils/Utils.php';



class ReportingEngine
{

    use Utils;

    protected $TL;
    protected $ReportEngineDB;
    public $Error;

    const POST_SCRIPTS_DIR = __DIR__ . '/scripts';

    /*
    **
    **
    **
    **
    **
    **
    ** Constructor
    ** ======================================
    */

    public function __construct($TL = false)
    {

        // Establish a global instance of TL
        $this->TL = $TL;


        $this->ReportEngineDB = $this->GetDatabaseConnection('Reporting');
    }
    /*
    **
    **
    **
    **
    **
    ** 
    **  Configure the databases
    ** ==================================
    */
    private function SetConfigurations()
    {

        $this->ReportEngineDB = $this->GetDatabaseConnection('Reporting');
        // // Determine if we are initiating this request from a local environment
        // $LocalMode = ($this->TL->Location['domainExtension'] === "local") ? true : false;

        // // Set the correct database for the correct environment
        // if ($LocalMode === true) {
        //     $this->ReportEngineDB = new \System\Database($this->TL, 'DEVELOPMENT-Reporting');
        // } else {
        //     $this->ReportEngineDB = new \System\Database($this->TL, 'PRODUCTION-Reporting');
        // }

        // Config all set
        return true;
    }
    /* 
    **
    **
    **
    **
    **
    ** 
    **  Verify all initialization has succeeded 
    ** ==================================
    */
    private function VerifyInit()
    {
        // Properties to verify
        $InitValues = [$this->ReportEngineDB];

        // Loop through and check none are empty
        foreach ($InitValues as $Value) {
            if (empty($Value)) {
                $this->Error = "Could not initialize data in constructor";
                $this->TL->Log($this->Error, 'ERROR');
                return false;
            }
        }

        // Initialization success!
        return true;
    }
    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    ** ==================================
    */
    public function GetReport(string $ReportName): ?array
    {
        $Query = "SELECT * FROM `Reports` WHERE `Name` = :Name";
        if (!$Result = $this->ReportEngineDB->Query($Query, [':Name' => $ReportName])) {
            $this->Error = "Unable to find Report $ReportName";
            $this->TL->Log($this->Error, 'ERROR');
            return null;
        }


        $Report = $Result[0] ?? [];
        $DecodedReport = json_decode($Report['Report Definition'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->Error = "Unable to decode Report $ReportName";
            $this->TL->Log($this->Error, 'ERROR');
            return null;
        }
        $Report['Report'] = $DecodedReport;
        return $Report;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    **  Pre process / sql / post process
    ** ==================================
    */
    public function Retrieve(array $Params): ?array
    {
        // Get the report name
        $ReportName = $Params['ReportName'] ?? null;

        // Get the report and extract the details
        if (!$Result = $this->GetReport($ReportName)) {
            return null;
        }
        $Report = $Result['Report'] ?? null;
        $PreProcess = $Report['pre_process'] ?? null;
        $PostProcess = $Report['post_process'] ?? null;
        $SQLParts = $Report['sql'] ?? null;
        $RawSQL = $Report['raw_sql'] ?? null;
        $PHPScripts = $Report['php_scripts'] ?? null;

        // Verify Params is an array prior to kick off
        $Params = is_array($Params) ? $Params : [];

        // Data return object
        $MasterResult = [];

        // Run any pre_processes... this is used to add data to Params....Passed by Ref in function
        if ($PreProcess) {
            $this->RunProcess($PreProcess, $Params, 'pre_process');
        }

        // Run any raw SQL queries
        if ($RawSQL) {
            $MasterResult = $this->ProcessRawSQL($RawSQL, $Params);
        } elseif ($SQLParts) {
            // Run any queries from structured SQL
            $QueryParts = $this->BuildQueryParts($SQLParts, $Params);
            $MasterResult = $this->ExecuteQuery($QueryParts, $SQLParts);
        }

        $MasterResult = is_array($MasterResult) ? $MasterResult : [];

        if ($PostProcess) {
            $this->RunProcess($PostProcess, $MasterResult, 'post_process');
        }
        if ($PHPScripts) {
            $MasterResult = $this->RunPHPScripts($PHPScripts, $MasterResult);
        }

        if (!is_array($MasterResult)) {
            $this->TL->Agent->Error("Something failed... data is in an unusable format");
        }
        return $MasterResult;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    ** ==================================
    */
    public function BuildQueryParts(array $Report, array $Params): ?array
    {
        $SelectParts = [];
        $JoinParts = [];
        $WhereParts = [];
        $GroupBy = [];
        $OrderBy = [];
        $Bindings = [];

        // Verify we have a source.. not source go straight to post processing functions
        if (empty($Report['source'])) {
            return [];
        }


        // Main source
        $mainAlias = $Report['source']['alias'];
        // $mainTable = "`{$Report['source']['database']}`.`{$Report['source']['table']}` AS {$mainAlias}";
        $mainTable = "`{$Report['source']['table']}` AS {$mainAlias}";

        // === SELECT: fields ===
        $RawFields = $Report['fields'] ?? [];
        $Fields = $this->GetFields($RawFields);
        $SelectParts = array_merge($SelectParts, $Fields);

        // === SELECT: computed aggregates ===
        $RawAggregates = $Report['aggregates'] ?? [];
        $Aggregates = $this->GetAggregates($RawAggregates);
        $SelectParts = array_merge($SelectParts, $Aggregates);

        // === SELECT: calculated metrics ===
        $RawMetrics = $Report['calculated_metrics'] ?? [];
        $CalculatedMetrics = $this->GetCalculatedMetrics($RawMetrics);
        // $this->TL->Agent->Exit(["Calc Met" => $CalculatedMetrics]);

        $SelectParts = array_merge($SelectParts, $CalculatedMetrics);



        // === JOINs ===
        $RawJoins = $Report['joins'] ?? [];
        $JoinParts = $this->GetJoins($RawJoins);


        // === FILTERS (with {{placeholders}}) ===
        $RawFilters = $Report['filters'] ?? [];
        $Filters = $this->GetFilters($RawFilters, $Params);


        $WhereParts = $Filters['Where Parts'] ?? [];
        $Bindings = $Filters['Bindings'] ?? [];


        // === GROUP BY ===
        $RawGroupBy = $Report['group_by'] ?? [];
        $GroupBy = $this->GetGroupBy($RawGroupBy);


        // === ORDER BY ===
        $RawOrderBy = $Report['order_by'] ?? [];
        $OrderBy = $this->GetOrderBy($RawOrderBy);


        // === Final SQL Assembly ===
        $SQL = "SELECT " . implode(", ", $SelectParts) .
            " FROM {$mainTable} " .
            (!empty($JoinParts) ? implode(" ", $JoinParts) : '') .
            (!empty($WhereParts) ? " WHERE " . implode(" AND ", $WhereParts) : '') .
            (!empty($GroupBy) ? " GROUP BY " . implode(", ", $GroupBy) : '') .
            (!empty($OrderBy) ? " ORDER BY " . implode(", ", $OrderBy) : '');


        // === LIMIT / OFFSET ===
        $IgnoreValues = ['', false, null];
        if (isset($Report['limit']) && !in_array($Report['limit'], $IgnoreValues, true)) {
            $SQL = $SQL . " LIMIT :limit ";
            $Bindings[':limit'] = (int)$Report['limit'];
        }
        if (isset($Report['offset']) && !in_array($Report['offset'], $IgnoreValues, true)) {
            $SQL = $SQL . " OFFSET :offset ";
            $Bindings[':offset'] = (int)$Report['offset'];
        }


        return [
            'Query' => $SQL,
            'Bindings' => $Bindings
        ];
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    ** ==================================
    */
    public function GetAggregates(array $Aggregates): array
    {
        // Verify it is an array and not empty
        if (!is_array($Aggregates) || empty($Aggregates)) {
            return [];
        }

        // Return values
        $SelectParts = [];

        // Loop through the aggregates adding to SQL
        foreach ($Aggregates as $Aggregate) {

            $Expression = '';
            $Function = '';
            // Get the alias for the aggregate
            $Alias = $Aggregate['alias'] ?? $Aggregate['name'] ?? 'computed';

            // If computed field exists, we have to pull the re-usable function from the DB
            if (isset($Aggregate['computed_field'])) {

                // Lookup in computed_fields table
                $Result = $this->ReportEngineDB->Query("SELECT Expression, `Aggregate Function` FROM `Computed Fields` WHERE Name = :Name", [':Name' => $Aggregate['computed_field']]);

                // Computed field could not be found in the DB... log error and skip aggregate
                if (!$Result) {
                    $this->Error = "Computed Field {$Aggregate['computed_field']} could not be found";
                    $this->TL->Log($this->Error, 'ERROR');
                    continue;
                }

                // Get the computed field result and extract the function (SUM,AVG,MAX...) and expression (total_sales - refunds)
                $ComputedField = $Result[0];
                $RawExpression = $ComputedField['Expression'];
                $Function = $ComputedField['Aggregate Function'] ?? null;

                // Bindings must be provided in the report JSON to map placeholders to real columns
                $Bindings = $Aggregate['bindings'] ?? [];

                // Replace placeholders like {{var}} with actual fields from bindings
                $Expression = preg_replace_callback('/{{\s*(\w+)\s*}}/', function ($matches) use ($Bindings, $Aggregate) {
                    $Key = $matches[1];
                    if (!isset($Bindings[$Key])) {
                        $this->TL->Log("Missing binding for placeholder {{$Key}} in computed_field '{$Aggregate['computed_field']}'", 'ERROR');
                        return 'NULL';
                    }
                    return $Bindings[$Key];
                }, $RawExpression);


                // Form the SQL for the aggregate
                $SQL = $Function ? "{$Function}({$Expression})" : $Expression;
            } else {
                // Aggregate is not in DB Table...Ad-hoc definition in report json
                $Expression = $Aggregate['expression'] ?? '';
                $Function = $Aggregate['aggregate_function'] ?? null;
                $SQL = $Function ? "{$Function}({$Expression})" : $Expression;
            }


            // Add the SQL to the Select Parts array
            $SelectParts[] = "{$SQL} AS `{$Alias}`";
        }

        return $SelectParts;
    }
    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get the requested report from the reporting DB
    ** ==================================
    */
    public function GetCalculatedMetrics(array $Metrics): array
    {
        // Verify it is an array and not empty
        if (!is_array($Metrics) || empty($Metrics)) {
            return [];
        }

        // Return values
        $SelectParts = [];

        // Loop through the aggregates adding to SQL
        foreach ($Metrics as $Metric) {

            $Expression = '';
            $Function = '';

            // Get the metric name to query from Computed Metrics table
            $Name = $Metric['name'] ?? null;
            // Get the metric name
            if (!$Name) {
                $this->Error = "Metric name is missing in input metric definition.";
                $this->TL->Log($this->Error, 'ERROR');
                continue;
            }

            // Get the alias for the aggregate
            $Alias = $Metric['alias'] ?? $Metric['name'] ?? 'computed';



            // Get the metric data
            if (!$Result = $this->ReportEngineDB->Query("SELECT Expression, `Aggregate Function` FROM `Calculated Metrics` WHERE Name = :Name", [':Name' => $Name])) {
                $this->Error = "Query failed for computed metric {$Name}.";
                $this->TL->Log($this->Error, 'ERROR');
                continue;
            }

            // Computed metric could not be found in the DB... log error and continue to the next metric

            if (!isset($Result[0]) || !isset($Result[0]['Expression'])) {
                $this->Error = "Computed metric '{$Name}' not found or malformed.";
                $this->TL->Log($this->Error, 'ERROR');
                continue;
            }
            // Get the computed field result and extract the function (SUM,AVG,MAX...) and expression (total_sales - refunds)
            $RawExpression = $Result[0]['Expression'];
            $Function = $Result[0]['Aggregate Function'] ?? null;

            $ValidFunctions = ['SUM', 'AVG', 'MIN', 'MAX', null];
            if (!in_array($Function, $ValidFunctions, true)) {
                $this->TL->Log("Invalid aggregate function for '{$Name}'", 'ERROR');
                continue;
            }


            // Bindings must be provided in the report JSON to map placeholders to real columns
            $Bindings = $Metric['bindings'] ?? [];

            $HasBindingError = false;
            // Replace placeholders like {{var}} with actual fields from bindings
            $Expression = preg_replace_callback('/{{\s*(\w+)\s*}}/', function ($matches) use ($Bindings, $Name, &$HasBindingError) {
                $Key = $matches[1];

                if (!isset($Bindings[$Key])) {
                    $this->TL->Log("Missing binding for placeholder $Key in computed metric $Name", 'ERROR');
                    $HasBindingError = true;
                    return 'NULL';
                }
                $BindParts = $Bindings[$Key];

                // Allow bind parts string expressions
                if (is_string($BindParts)) {
                    return $BindParts;
                }

                // Do not have string, verify we have components to stich together the metric source
                if (empty($BindParts['source']) || empty($BindParts['column'])) {
                    $this->TL->Log("Missing bind components for key {$Key} in computed metric $Name", 'ERROR');
                    $HasBindingError = true;
                    return 'NULL';
                }

                return "{$BindParts['source']}.`{$BindParts['column']}`";
            }, $RawExpression);
            if ($HasBindingError) {
                $this->TL->Log("One or more bindings missing or malformed in computed metric '{$Name}'", 'ERROR');
                continue;
            }

            // Form the SQL for the aggregate
            $SQL = $Function ? "{$Function}({$Expression})" : $Expression;


            // Add the SQL to the Select Parts array
            $SelectParts[] = "{$SQL} AS `{$Alias}`";
        }

        return $SelectParts;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function ProcessRawSQL($RawSQL, $Params)
    {

        // Extract raw sql parts and check for required parts
        $Query = $RawSQL['query'] ?? null;
        $Database = $RawSQL['database'] ?? null;
        $RawBindings = $RawSQL['bindings'] ?? [];
        if (!$Query) {
            $this->Error = "Raw SQL is missing required param";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }

        // Create a connection the to the DB source if needed.... verify
        $Connection = $Database ? $this->GetDatabaseConnection($Database) : $this->TL->Database;
        if (!$Connection) {
            $this->Error = "Failed to create DB connection for Raw SQL.";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }

        // Create the bindings
        $Bindings = [];
        $ParamCounter = 0;

        foreach ($RawBindings as $Key => $Value) {
            $BindValue = $this->ResolvePlaceholder($Value, $Params);
            if (is_array($BindValue)) {


                $Placeholders = [];
                foreach ($BindValue as $Index => $V) {
                    $ArrayKey = ":param{$ParamCounter}";
                    $Placeholders[] = $ArrayKey;
                    $Bindings[$ArrayKey] = $V;
                    $ParamCounter++;
                }
                $InClause = "(" . implode(',', $Placeholders) . ")";
                $Query = preg_replace('/' . preg_quote($Key, '/') . '\b/', $InClause, $Query, 1);
                // $Query = str_replace($Key, $InClause, $Query);
            } else {
                $Bindings[$Key] = $BindValue;
            }
        }

        // Execute the query and return results
        if (!$Result = $Connection->Query($Query, $Bindings)) {
            $this->TL->Agent->Exit(['Query1' => $Query, 'Bindings1' => $Bindings, 'Result1' => $Result, 'DB Error' => $Connection->Error]);
            $this->Error = "Raw SQL query '$Query' failed to produce a result.";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }
        return $Result;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function GetFields(array $Fields): array
    {
        // Verify it is an array and not empty
        if (!is_array($Fields) || empty($Fields)) {
            return [];
        }

        // Return values
        $SelectParts = [];

        // Loop through the selected fields and add them to the Select Parts
        foreach ($Fields as $Field) {
            $Alias = isset($Field['alias']) ? " AS `{$Field['alias']}`" : '';
            $SelectParts[] = "{$Field['source']}.`{$Field['column']}`{$Alias}";
        }
        return $SelectParts;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function GetGroupBy(array $Groups): array
    {
        // Verify it is an array and not empty
        if (!is_array($Groups) || empty($Groups)) {
            return [];
        }

        $GroupBy = [];

        foreach ($Groups as $Group) {
            $GroupBy[] = $Group;
        }
        return $GroupBy;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function GetOrderBy(array $Orders): array
    {
        // Verify it is an array and not empty
        if (!is_array($Orders) || empty($Orders)) {
            return [];
        }

        $OrderBy = [];

        foreach ($Orders as $Order) {
            $OrderBy[] = "`{$Order['field']}` " . strtoupper($Order['direction']);
        }
        return $OrderBy;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function GetJoins(array $Joins): array
    {
        // Verify it is an array and not empty
        if (!is_array($Joins) || empty($Joins)) {
            return [];
        }

        // Return values
        $JoinParts = [];

        // Loop through the selected fields and add them to the Select Parts
        foreach ($Joins as $Join) {

            // Fully-qualified join clause
            $JoinClause = strtoupper(($Join['type'] ?? '')) . " JOIN `{$Join['source']['table']}` AS {$Join['source']['alias']}";
            $JoinParts[] = "{$JoinClause} ON {$Join['on']}";
        }
        return $JoinParts;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function GetFilters(array $Filters, $Params): array
    {


        // Verify it is an array and not empty
        if (!is_array($Filters) || empty($Filters)) {
            return [];
        }

        // Return values
        $WhereParts = [];
        $Bindings = [];
        $ParamCounter = 0;

        // Loop through the Filters, resolve the value, create the WHERE clause and add to the bindings
        foreach ($Filters as $Filter) {
            // Get the field / column name
            $Field = $Filter['field'];
            // Transform operator to uppercase
            $Operator = strtoupper($Filter['operator'] ?? '=');


            // Get the value... could be array, placeholder may need to be resolved {{User ID}}
            $Value = $Filter['value'];

            // === Between Operator ===
            // See if we have 2 dates
            if ($Operator === 'BETWEEN' && is_array($Value) && count($Value) === 2) {

                // Resolve the 2 dates
                $Value1 = $this->ResolvePlaceholder($Value[0], $Params);
                $Value2 = $this->ResolvePlaceholder($Value[1], $Params);
                // Create the binding param for the query
                $Param1 = ":param" . $ParamCounter++;
                $Param2 = ":param" . $ParamCounter++;
                // Add the clause to the Where Parts
                $WhereParts[] = "{$Field} BETWEEN {$Param1} AND {$Param2}";
                // Update the Bindings
                $Bindings[$Param1] = $Value1;
                $Bindings[$Param2] = $Value2;
            } elseif ($Operator === 'IN') {
                $Resolved = $this->ResolvePlaceholder($Value, $Params);
                if (is_array($Resolved)) {
                    foreach ($Resolved as $Index => $V) {
                        $Key = ":param{$ParamCounter}";
                        $Placeholders[] = $Key;
                        $Bindings[$Key] = $V;
                        $ParamCounter++;
                    }
                    $InClause = implode(',', $Placeholders);
                    $WhereParts[] = "{$Field} IN ({$InClause})";
                }
            } else {
                // Not BETWEEN:  just need to resolve placeholder and create bindings
                $Resolved = $this->ResolvePlaceholder($Value, $Params);

                $Param = ":param" . $ParamCounter++;
                $WhereParts[] = "{$Field} {$Operator} {$Param}";
                $Bindings[$Param] = $Resolved;
            }
        }
        // $this->TL->Agent->Exit(['Where Parts' => $WhereParts, 'Bindings' => $Bindings]);
        return ['Where Parts' => $WhereParts, 'Bindings' => $Bindings];
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function ResolvePlaceholder($Value, $Params)
    {
        // $this->TL->Agent->Exit(['Value' => $Value, 'Params' => $Params]);
        // Match our {{Variable}}
        if (is_string($Value) && preg_match('/{{(.+?)}}/', $Value, $matches)) {

            $Key = $matches[1];
            // This is needed because spaces are replaced with '_' during our Agent ajax XHR request
            $Key = $this->RemoveSpacesFromKey($Key);
            if (!array_key_exists($Key, $Params)) {

                $this->Error = "Missing runtime parameter: {$Key}";
                $this->TL->Log($this->Error, 'ERROR');
                return null;
            }

            return $Params[$Key];
        }
        return $Value;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function AddSpacesInKey($Key)
    {
        return str_replace('__', ' ', $Key);
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function RemoveSpacesFromKey($Key)
    {
        return str_replace(' ', '_', $Key);
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function GetDatabaseConnection($DatabaseName)
    {
        $Connection = null;
        // Determine if we are initiating this request from a local environment
        $IsLocal = ($this->TL->Location['domainExtension'] === "local") ? true : false;
        $Environment = $IsLocal ? 'DEVELOPMENT' : 'PRODUCTION';
        $ConnectionName = "{$Environment}-{$DatabaseName}";

        try {
            // Set the correct database for the correct environment
            $Connection = new \System\Database($this->TL, $ConnectionName);
        } catch (\Throwable $th) {
            $this->Error = "Something failed creating a database connection: $ConnectionName";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }
        // Config all set
        return $Connection;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function ExecuteQuery($QueryComponents, $SQLParts)
    {

        // Verify required params
        $RequiredParams = ['Query'];
        if (!$this->ValidateRequiredParams($RequiredParams, $QueryComponents)) {
            return null;
        }


        $Query = $QueryComponents['Query'];
        $Bindings = $QueryComponents['Bindings'] ?? [];



        // Get the source database from the Report
        if (!$DatabaseName = isset($SQLParts['source']['database']) ? $SQLParts['source']['database'] : null) {
            $this->Error = "Could not create a database connection: {$SQLParts['source']['database']}";
            $this->TL->Log($this->Error, 'ERROR');
            return null;
        }
        // $this->TL->Agent->Exit(['Query' => $Query, "Bindings" => $Bindings, 'DBname' => $DatabaseName]); //DEV NOTE: DELETE ME☹️

        // Create a connection
        $Connection = $this->GetDatabaseConnection($DatabaseName);

        if (!$Result = $Connection->Query($Query, $Bindings)) {
            $this->TL->Agent->Exit(["Query Error:" => json_encode($Connection->Error), 'Query' => $Query, "Bindings" => $Bindings, 'Result' => $Result]); //DEV NOTE: DELETE ME☹️

            $this->Error = "Query Failed: $Query";
            $this->TL->Log($this->Error, 'ERROR');
            return null;
        }
        return $Result;
        // $this->TL->Agent->Exit(["Result" => $Result]);
        // Determine if we are initiating this request from a local environment
        // $LocalMode = ($this->TL->Location['domainExtension'] === "local") ? true : false;

        // // Set the correct database for the correct environment
        // if ($LocalMode === true) {
        //     $Connection = new \System\Database($this->TL, "DEVELOPMENT-$Source");
        // } else {
        //     $Connection = new \System\Database($this->TL, "PRODUCTION-$Source");
        // }

        // // Config all set
        // return $Connection;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function ValidateRequiredParams($Required, $Params)
    {
        if (empty($Required) ||  empty($Params)) {
            $this->Error = "Missing required Parameters: Input arrays are empty";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }
        foreach ($Required as $Index => $Require) {
            if (!array_key_exists($Require, $Params)) {
                $this->Error = "Missing required Param {$Require}";
                $this->TL->Log($this->Error, 'ERROR');
                return false;
            }
        }
        return true;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Runs php scripts...NOTE: Scripts have direct access to $DATA... PASSED BY REFERENCE
    ** ==================================
    */
    public function RunPHPScripts(array $Scripts, &$DATA = [])
    {
        // Get the base directory of the post process scripts and normalize it across different operating systems
        $BaseDir = realpath(self::POST_SCRIPTS_DIR);

        if (!$BaseDir) {
            $this->TL->Log("Invalid post scripts base directory", 'ERROR');
            return $DATA;
        }

        if (!is_array($Scripts)) {
            $this->TL->Log("Error:  post_process scripts are not in the proper format... expected array", 'ERROR');
            return $DATA;
        }

        // Ensure $DATA is an array
        if (!is_array($DATA)) {
            $DATA = [];
        }
        // Loop through all script names
        foreach ($Scripts as $ScriptName) {
            // Get the script name.. ex. TestScript
            // if (!$ScriptName) {
            //     $this->TL->Log("Missing script name in php_scripts entry", 'ERROR');
            //     continue;
            // }
            // $this->TL->Agent->Exit(['BaseDir' => $BaseDir, 'ScriptNme' => $ScriptName]);

            // Build the relative filepath add extension .... some/path/to/TestScript.php
            $RelativeFilePath = $BaseDir . DIRECTORY_SEPARATOR . $ScriptName . DIRECTORY_SEPARATOR . $ScriptName . ".php";

            // Resolve the relative filepath to the absolute file path
            $AbsoluteFilePath = realpath($RelativeFilePath);

            // Validate path..
            // Verify we have a path...Verify the absolute path starts with the base path
            if (!$AbsoluteFilePath || strpos($AbsoluteFilePath, $BaseDir) !== 0) {
                $this->TL->Log("Script path traversal or not found: {$ScriptName}", 'ERROR');
                continue;
            }

            // Require the script... the script may exit or modify the $DATA reference...NOTE:  PASSED BY REFERENCE so scripts have direct access
            try {
                require_once $AbsoluteFilePath;
            } catch (Throwable $e) {
                // Includes both Exception and Error
                $this->TL->Log("Error loading script {$ScriptName}: " . $e->getMessage(), 'ERROR');
                continue;
            }
        }
        // This will return the $DATA object...It will only execute if the scripts do not exit prior... requires any script that executes to modify the
        // Shared $DATA variable to be returned
        return $DATA;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  
    ** ==================================
    */
    public function PostProcessing(array $PostProcess, $Data)
    {
        // Validate required keys
        if (empty($PostProcess['script']) || empty($PostProcess['functions'])) {
            $this->Error = "Missing required post_process param";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }

        // Sanitize script name
        $script = basename($PostProcess['script']); // removes ../ or dangerous chars
        if (!preg_match('/^[\w\-]+$/', $script)) {
            $this->Error = "Invalid script name in post_process: {$script}";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }

        // Build full path and validate it exists
        $scriptPath = __DIR__ . "/post_process/{$script}.php";
        if (!file_exists($scriptPath) || !realpath($scriptPath)) {
            $this->Error = "Post-process script not found: {$scriptPath}";
            $this->TL->Log($this->Error, 'ERROR');
            return false;
        }

        // Include the utility file
        require_once $scriptPath;

        // Loop through all functions and apply them to the data
        foreach ((array)$PostProcess['functions'] as $function) {
            if (!function_exists($function)) {
                $this->Error = "Function {$function} not found in script {$script}";
                $this->TL->Log($this->Error, 'ERROR');
                return false;
            }

            // Call the function, passing in $Data and optionally more context
            $Data = $function($Data);
        }

        return $Data;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Runs Pre/Post processes... Mutates the $DataRef array....PASSED BY REFERENCE
    ** ==================================
    */
    public function RunProcess(array $Processes,  &$DataRef = [], $Phase = 'pre_process'): void
    {
        // Validate required keys
        if (!is_array($Processes) || empty($Processes)) {
            $this->TL->Log("Error:  $Phase is not in the correct format", 'ERROR');
            return;
        }

        // Loop through the pre_process array executing the functions
        foreach ($Processes as $Index => $Process) {
            // Get the function name and output key
            $Function = $Process['function'] ?? null;
            $OutputKey = $Process['output_key'] ?? $Function;


            // Verify function exists
            if (!$Function) {
                $this->Error = "Missing required function param in {$Phase}";
                $this->TL->Log($this->Error, 'ERROR');
                continue;
            }

            // Verify method exists on this
            if (!method_exists($this, $Function)) {
                $this->TL->Log("Method '{$Function}' not found in step {$Phase}", 'ERROR');
                continue;
            }

            // Call the function and update the $DataRef if the function returned value
            $Result = $this->$Function($DataRef, $OutputKey);
            if (!is_array($Result)) {
                $this->TL->Log("Function '{$Function}' did not return array in {$Phase}", 'ERROR');
                continue;
            }
            // Update the Data...note: passed by reference
            $DataRef = $Result;
        }
        return;
    }

    /* 
    **
    **
    **
    **
    **
    ** 
    **  Get a users reports based on permission
    ** ==================================
    */
    public function GetUserReports()
    {

        // Set the query
        $Query = "SELECT `Name`, `Display Name`, `Description`, `Category`, `Format`, `Icon`, `Version`, `Permissions`  FROM `Reports` WHERE `Is Active` = 1";

        // Execute the query
        if (!$Result = $this->ReportEngineDB->Query($Query)) {
            $this->Error = "No active reports found";
            return false;
        }

        // Main return array
        $Reports = [];

        // Add the reports the user has permissions for to return object
        foreach ($Result as $Index => $Row) {
            if (empty($Row['Permissions'])) continue;

            // Add the report if user has permission
            if ($this->VerifyReportPermissions($Row['Permissions'])) {
                // Remove permission.. not send to the client
                unset($Row['Permissions']);

                // Index reports by category;
                $Reports[$Row['Category']] ??= [];
                $Reports[$Row['Category']][] = $Row;
            }
        }

        // Return result
        return $Reports;
    }
    /* 
    **
    **
    **
    **
    **
    ** 
    **  Return bool user based permission
    ** ==================================
    */
    public function VerifyReportPermissions(string $Permissions = ''): bool
    {
        // Get the reports permissions in an array
        $Explode = array_map('trim', explode(',', $Permissions));

        // Verify the client's permission matches an array entry
        if ($this->TL->User->VerifyPermissions($Explode)) {
            return true;
        }
        return false;
    }
}
