<?php

// Unpack Reporting Engine
$TL->Unpack(['ReportingEngine' => ['ReportingEngine']]);
// $Result = $TL->ReportingEngine->GetUserReports();
// $TL->Agent->Exit($Result);

// // Get the post data
$POST = $TL->Agent->Input;
if (empty($POST['ReportName']))
    $TL->Agent->Error("Invalid parameters");


// Retrieve the report
if (!$Result = $TL->ReportingEngine->Retrieve($POST)) {
    $TL->Agent->Error($TL->ReportingEngine->Error);
}
$TL->Agent->Exit($Result);
