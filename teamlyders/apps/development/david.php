<link rel="stylesheet" href="resource?src=styles/david.css">
<div class="container" style="display: flex; flex-wrap:wrap; gap: 25px; height: 100vh; align-items:center; justify-content:center;align-content:center;">
    <!-- <div><button class="TL-Button comp-agg">Computed and Aggregate</button></div> -->
    <div><button class="TL-Button user-punches">User Punches</button></div>
    <div><button class="TL-Button user-punches-join-user-table">User Punches Join User Table</button></div>
    <div><button class="TL-Button my-stores-sales">My Stores Sales</button></div>
    <div><button class="TL-Button stores-list">Store List</button></div>
    <div><button class="TL-Button raw-sql-dynamic">Raw Sql Dynamic Params</button></div>
    <div><button class="TL-Button raw-sql-defined">Raw SQL Defined Params</button></div>
    <div><button class="TL-Button raw-sql-no">Raw SQL No Params</button></div>
    <div><button class="TL-Button showstoppers">Showstoppers</button></div>
    <div><button class="TL-Button store-contacts">Store Contacts</button></div>
    <!-- <div><button class="TL-Button stores-list">Old Stores List</button></div> -->
    <!-- <div><button class="TL-Button stores-list-add-one">Add 1 to Store IDs</button></div> -->
    <!-- <div><button class="TL-Button daily-business-summary">DAILY BUSINESS SUMMARY</button></div> -->
    <!-- <div><button class="TL-Button daily-business-summary-join">DAILY BUSINESS SUMMARY Join 2 DBs</button></div> -->
    <!-- <div><button class="TL-Button my-stores">My Stores</button></div> -->
    <!-- <div><button class="TL-Button example">Example</button></div> -->
    <!-- <div><button class="TL-Button example-min">Example Min</button></div> -->
</div>




<script>
    TL.Ready(['Inputs', "Downloader"], () => {

        $(document).on("click", ".TL-Button.showstoppers", function() {
            TL.Agent({
                agent: ["app", "xformity"],
                data: {
                    ReportName: 'Showstoppers',
                },
                success(Result) {
                    console.log("RESULT", Result);
                },
                //Agent returned an error, pass it to the user
                error(error) {
                    TL.Notify.Banner("Error", error);
                },
            });
        });
        $(document).on("click", ".TL-Button.store-contacts", function() {
            TL.Agent({
                agent: ["app", "xformity"],
                data: {
                    ReportName: 'Store Contacts',
                },
                success(Result) {
                    console.log("RESULT", Result);
                },
                //Agent returned an error, pass it to the user
                error(error) {
                    TL.Notify.Banner("Error", error);
                },
            });
        });
        $(document).on("click", ".TL-Button.raw-sql-no", function() {
            TL.Agent({
                agent: ["app", "xformity"],
                data: {
                    ReportName: 'Raw Sql No Params',
                },
                success(Result) {
                    console.log("RESULT", Result);
                },
                //Agent returned an error, pass it to the user
                error(error) {
                    TL.Notify.Banner("Error", error);
                },
            });
        });
        $(document).on("click", ".TL-Button.raw-sql-defined", function() {
            TL.Agent({
                agent: ["app", "xformity"],
                data: {
                    ReportName: 'Raw Sql Defined Params',
                    Status: 'ACTIVE',
                    Title: "RGM"
                },
                success(Result) {
                    console.log("RESULT", Result);
                },
                //Agent returned an error, pass it to the user
                error(error) {
                    TL.Notify.Banner("Error", error);
                },
            });
        });
        $(document).on("click", ".TL-Button.raw-sql-dynamic", function() {
            TL.Agent({
                agent: ["app", "xformity"],
                data: {
                    ReportName: 'Raw Sql Dynamic Params',
                    Status: 'ACTIVE',
                    Title: "RGM"
                },
                success(Result) {
                    console.log("RESULT", Result);
                },
                //Agent returned an error, pass it to the user
                error(error) {
                    TL.Notify.Banner("Error", error);
                },
            });
        });
        $(document).on("click", ".TL-Button.my-stores-sales", function() {
            TL.Agent({
                agent: ["app", "xformity"],
                data: {
                    ReportName: 'My Stores Sales',
                    From: '2025-07-01',
                    To: '2025-07-02',
                },
                success(Result) {
                    console.log("RESULT", Result);
                },
                //Agent returned an error, pass it to the user
                error(error) {
                    TL.Notify.Banner("Error", error);
                },
            });
        });
        $(document).on("click", ".TL-Button.stores-list", function() {
            TL.Agent({
                agent: ["app", "xformity"],
                data: {
                    ReportName: 'Stores List',
                },
                success(Result) {
                    console.log("RESULT", Result);
                },
                //Agent returned an error, pass it to the user
                error(error) {
                    TL.Notify.Banner("Error", error);
                },
            });
        });
        // $(document).on("click", ".TL-Button.my-stores", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'My Stores',
        //             ["User ID"]: 226053,
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        // $(document).on("click", ".TL-Button.daily-business-summary-join", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'Dpoll Join',
        //             From: '2024-09-01',
        //             To: '2024-09-10',
        //             ["Store ID"]: 867,
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        // $(document).on("click", ".TL-Button.daily-business-summary", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'DAILY BUSINESS SUMMARY',
        //             From: '2024-09-01',
        //             To: '2024-09-10',
        //             ["Store ID"]: 867,
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        // $(document).on("click", ".TL-Button.stores-list-add-one", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'Stores List Add One',
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        $(document).on("click", ".TL-Button.user-punches-join-user-table", function() {
            TL.Agent({
                agent: ["app", "xformity"],
                data: {
                    ReportName: 'User Punches Join User Table',
                    From: '2025-06-12',
                    To: '2025-06-18',
                    ["User ID"]: 226053,
                },
                success(Result) {
                    console.log("RESULT", Result);
                },
                //Agent returned an error, pass it to the user
                error(error) {
                    TL.Notify.Banner("Error", error);
                },
            });
        });
        $(document).on("click", ".TL-Button.user-punches", function() {
            TL.Agent({
                agent: ["app", "xformity"],
                data: {
                    ReportName: 'User Punches',
                    From: '2025-06-12',
                    To: '2025-06-18',
                    ["User ID"]: 226053,
                },
                success(Result) {
                    console.log("RESULT", Result);
                },
                //Agent returned an error, pass it to the user
                error(error) {
                    TL.Notify.Banner("Error", error);
                },
            });
        });
        // $(document).on("click", ".TL-Button.store-punches", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'Store Punches',
        //             From: '2025-06-12',
        //             To: '2025-06-18',
        //             ["Store ID"]: 867,
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        // $(document).on("click", ".TL-Button.store-revenue", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'Store Revenue',
        //             From: '2025-07-27',
        //             To: '2025-07-28',
        //             ["Store ID"]: 5,
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        // $(document).on("click", ".TL-Button.stores-revenue", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'Stores Revenue',
        //             From: '2025-07-27',
        //             To: '2025-07-28',
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        // $(document).on("click", ".TL-Button.store-sales", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'Store Sales',
        //             From: '2025-07-27',
        //             To: '2025-07-28',
        //             ["Store ID"]: 5,
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        // $(document).on("click", ".TL-Button.stores-sales", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'Stores Sales',
        //             From: '2025-07-27',
        //             To: '2025-07-28',
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        // $(document).on("click", ".TL-Button.store-refunds", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'Store Refunds',
        //             From: '2025-07-27',
        //             To: '2025-07-28',
        //             ["Store ID"]: 5,
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });
        // $(document).on("click", ".TL-Button.stores-refunds", function() {
        //     TL.Agent({
        //         agent: ["app", "xformity"],
        //         data: {
        //             ReportName: 'Stores Refunds',
        //             From: '2025-07-27',
        //             To: '2025-07-28',
        //         },
        //         success(Result) {
        //             console.log("RESULT", Result);
        //         },
        //         //Agent returned an error, pass it to the user
        //         error(error) {
        //             TL.Notify.Banner("Error", error);
        //         },
        //     });
        // });




    });
</script>