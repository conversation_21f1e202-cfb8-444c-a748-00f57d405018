/* ========================================================================
	index.css - Reporting App Main Page Styles
	======================================================================== */

/* ------------------------------------------------------------------------
	1. Main App Layout (Inherits from reporting-shared.css)
	------------------------------------------------------------------------ */

/* Main App Container Override */
.reporting-app {
	display: flex;
	/* Use a fixed viewport-height container so internal areas control scrolling */
	height: 100vh;
	overflow: hidden; /* prevent the page body from becoming the scroll container */
	background: #fff;
}

/* Main Content Area Overrides */
/* NOTE: selector fixed from `.reporting-app.main-content` to target the .main-content
   element inside the .reporting-app container. Ensure this area is the only scrollable
   panel inside the full-height flex container. */
.reporting-app .main-content {
	flex: 1 1 auto;
	padding: 30px 32px 64px;
	/* Allow the flex child to shrink below its content height so overflow works correctly */
	min-height: 0;
	overflow-y: auto;
}

/* If other rules target just `.main-content`, keep them consistent */
.main-content {
	min-height: 0; /* safety for nested usage */
}

/* ------------------------------------------------------------------------
	2. Sidebar Navigation
	------------------------------------------------------------------------ */

/* Sidebar Container */
.sidebar {
	width: 240px;
	background: #fafafa;
	border-right: 1px solid #e5e5e5;
	padding: 24px 0;
	/* Keep the sidebar visually pinned while letting its internal content scroll if needed */
	position: sticky;
	top: 0;
	height: 100vh;
	overflow-y: auto;
	/* ensure the sidebar doesn't flex-grow/shrink unexpectedly inside the flex container */
	flex: 0 0 240px;
}

/* Sidebar Header */
.sidebar-header {
	padding: 0 20px 20px 20px;
	border-bottom: 1px solid #e5e5e5;
	margin-bottom: 12px;
}

.sidebar-header h1 {
	font-size: 24px;
	margin: 0;
	color: #262626;
	font-weight: 600;
}

/* ------------------------------------------------------------------------
	3. Category Navigation
	------------------------------------------------------------------------ */

/* Favorites Section */
.favorites-section {
	padding: 0 12px;
	margin-bottom: 16px;
}

.favorites-list {
	padding: 0;
	margin: 0;
	list-style: none;
}

/* Category Separator */
.category-separator {
	height: 1px;
	background: #e5e5e5;
	margin: 0 12px 16px 12px;
	position: relative;
}

.category-separator::before {
	content: "Categories";
	position: absolute;
	top: -8px;
	left: 8px;
	background: #fafafa;
	padding: 0 8px;
	font-size: 11px;
	font-weight: 600;
	color: #8b9dc3;
	letter-spacing: 0.5px;
	text-transform: uppercase;
}

/* Categories Section */
.categories-section {
	padding: 0 12px;
}

/* Category List Container */
.category-list {
	list-style: none;
	margin: 0px;
	padding-top: 0px;
	padding-bottom: 40px;
}

/* Category Item */
.category-item {
	padding: 14px 16px;
	margin-bottom: 6px;
	cursor: pointer;
	border-radius: 12px;
	transition: all 0.2s ease;
	position: relative;
	background: transparent;
	border: 1px solid transparent;
}

/* Category Item Hover State */
.category-item:hover {
	background: #f5f5f5;
	color: #525252;
	border: 1px solid #d1d5db;
}

.category-item:hover .category-name {
	color: #525252;
	font-weight: 600;
}

.category-item:hover .category-count {
	background: #d1d5db;
	color: #525252;
}

/* Category Item Active State */
.category-item.active {
	background: #f5f5f5;
	color: #525252;
	border: 1px solid #d1d5db;
}

.category-item.active .category-name {
	color: #525252;
	font-weight: 600;
}

/* Category Name */
.category-name {
	font-size: 14px;
	color: #525252;
	margin: 0 0 4px 0;
	font-weight: 600;
}

/* Category Count Badge */
.category-count {
	font-size: 11px;
	color: #6b7280;
	background: #f3f4f6;
	padding: 4px 8px;
	border-radius: 12px;
	display: inline-block;
	min-width: auto;
	text-align: center;
	font-weight: 500;
	transition: all 0.2s ease;
	margin-top: 2px;
}

/* Category Count Interactive States */
.category-item:hover .category-count,
.category-item.active .category-count {
	background: #e5e7eb;
	color: #4b5563;
}

/* ------------------------------------------------------------------------
	4. Dark Theme Overrides
	------------------------------------------------------------------------ */

/* Dark Theme App Container */
.reporting-app.dark {
	background: #1a1a1a;
}

/* ------------------------------------------------------------------------
	4.1. Dark Sidebar Styles
	------------------------------------------------------------------------ */

/* Dark Sidebar Container */
.reporting-app.dark .sidebar {
	background: #2a2a2a;
	border-right: 1px solid #404040;
}

/* Dark Sidebar Header */
.reporting-app.dark .sidebar-header {
	border-bottom: 1px solid #404040;
}

.reporting-app.dark .sidebar-header h1 {
	color: #f5f5f5;
}

/* ------------------------------------------------------------------------
	4.2. Dark Category Navigation
	------------------------------------------------------------------------ */

/* Dark Category Separator */
.reporting-app.dark .category-separator {
	background: #404040;
}

.reporting-app.dark .category-separator::before {
	background: #2a2a2a;
	color: #a3a3a3;
}

/* Dark Category Item Hover State */
.reporting-app.dark .category-item:hover {
	background: #404040;
	color: #f5f5f5;
	border: 1px solid #525252;
}

.reporting-app.dark .category-item:hover .category-name {
	color: #f5f5f5;
}

.reporting-app.dark .category-item:hover .category-count {
	background: #525252;
	color: #f5f5f5;
}

/* Dark Category Item Active State */
.reporting-app.dark .category-item.active {
	background: #404040;
	color: #f5f5f5;
	border: 1px solid #525252;
}

.reporting-app.dark .category-item.active .category-name {
	color: #f5f5f5;
}

/* Dark Category Name */
.reporting-app.dark .category-name {
	color: #f5f5f5;
}

/* Dark Category Count Badge */
.reporting-app.dark .category-count {
	color: #a3a3a3;
	background: #404040;
}

/* Dark Category Count Interactive States */
.reporting-app.dark .category-item:hover .category-count,
.reporting-app.dark .category-item.active .category-count {
	background: #525252;
	color: #f5f5f5;
}

/* ------------------------------------------------------------------------
	5. Mobile Responsive Styles
	------------------------------------------------------------------------ */

/* Mobile Sidebar Toggle Button */
.sidebar-toggle {
	display: none;
	position: fixed;
	top: 20px;
	left: 20px;
	z-index: 1001;
	background: #fff;
	border: 1px solid #e5e5e5;
	border-radius: 12px;
	padding: 12px;
	cursor: pointer;
	transition: all 0.2s ease;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	width: 48px;
	height: 48px;
	align-items: center;
	justify-content: center;
}

.sidebar-toggle:hover {
	background: #f8f9fa;
	border-color: #d1d5db;
}

.sidebar-toggle svg {
	color: #525252;
	transition: all 0.2s ease;
}

/* Mobile Sidebar Backdrop */
.sidebar-backdrop {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.5);
	z-index: 999;
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
}

.sidebar-backdrop.show {
	opacity: 1;
	pointer-events: auto;
}

/* Mobile Responsive Breakpoints */
@media (max-width: 900px) {
	/* Show mobile controls */
	.sidebar-toggle {
		display: flex;
	}

	.sidebar-backdrop {
		display: block;
	}

	/* Hide sidebar by default on mobile */
	.sidebar {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 1000;
		transform: translateX(-100%);
		transition: transform 0.3s ease;
		box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
	}

	/* Show sidebar when open */
	.sidebar.open {
		transform: translateX(0);
	}

	/* Adjust main content for mobile */
	.reporting-app .main-content {
		width: 100%;
		padding: 80px 20px 32px;
	}

	/* Mobile-friendly touch targets */
	.category-item {
		padding: 16px 18px;
		margin-bottom: 8px;
	}

	.category-name {
		font-size: 15px;
	}

	.category-count {
		font-size: 12px;
		padding: 5px 10px;
	}
}

@media (max-width: 640px) {
	/* Smaller padding on very small screens */
	.reporting-app .main-content {
		padding: 80px 16px 24px;
	}

	/* Adjust sidebar width for small screens */
	.sidebar {
		width: 280px;
		flex: 0 0 280px;
	}

	/* Smaller toggle button on small screens */
	.sidebar-toggle {
		width: 44px;
		height: 44px;
		padding: 10px;
		top: 16px;
		left: 16px;
	}

	/* Mobile-optimized reports grid */
	.reports-grid {
		grid-template-columns: 1fr;
		gap: 16px;
	}

	/* Adjust content header for mobile */
	.content-header h2 {
		font-size: 24px;
	}

	.content-header p {
		font-size: 13px;
	}
}

@media (max-width: 480px) {
	/* Extra small screens */
	.sidebar {
		width: 260px;
		flex: 0 0 260px;
	}

	.reporting-app .main-content {
		padding: 80px 12px 20px;
	}

	/* Even smaller reports grid gap */
	.reports-grid {
		gap: 12px;
	}
}

/* ------------------------------------------------------------------------
	5.1. Dark Mode Mobile Styles
	------------------------------------------------------------------------ */

/* Dark Mobile Sidebar Toggle */
.reporting-app.dark .sidebar-toggle {
	background: #2a2a2a;
	border-color: #404040;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
}

.reporting-app.dark .sidebar-toggle:hover {
	background: #404040;
	border-color: #525252;
}

.reporting-app.dark .sidebar-toggle svg {
	color: #f5f5f5;
}

/* Dark Mobile Sidebar Backdrop */
.reporting-app.dark .sidebar-backdrop {
	background: rgba(0, 0, 0, 0.7);
}

/* Dark Mobile Sidebar Shadow */
@media (max-width: 900px) {
	.reporting-app.dark .sidebar {
		box-shadow: 2px 0 12px rgba(0, 0, 0, 0.3);
	}
}
