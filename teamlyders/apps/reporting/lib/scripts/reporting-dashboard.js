TL.Ready(["Charts"], function () {
	TL.AddScript("resource?src=scripts/reporting-shared.js", function () {
		/*
		 **
		 ** Reporting Dashboard Application
		 ** ==============================
		 ** Main dashboard for the reporting app featuring overview cards,
		 ** favorite reports, and relevant report categories based on job role.
		 */

		// Define global state for dashboard data
		const State = {
			DashboardData: null,
			CurrentJobTitle: null,
		};

		// Navigation object to handle routing actions
		const Navigation = {
			ToReporting: (CategoryKey) => {
				TL.Browser.Navigate(`reporting?category=${CategoryKey}`);
			},
			ToReportsPage: () => {
				TL.Browser.Navigate("reporting");
			},
			ToFavoritesPage: () => {
				TL.Browser.Navigate("reporting?category=favorites");
			},
		};

		// Mock data for dashboard
		const DashboardData = {
			overview: [
				{
					title: "Total Reports",
					value: "47",
					change: "+3 this month",
					changeType: "positive",
					icon: "📊",
					iconClass: "reports",
				},
				{
					title: "Reports Generated",
					value: "1,284",
					change: "+156 this month",
					changeType: "positive",
					icon: "📈",
					iconClass: "generated",
				},
				{
					title: "Favorite Reports",
					value: "12",
					change: "+2 this week",
					changeType: "positive",
					icon: "⭐",
					iconClass: "favorites",
				},
				{
					title: "Active Users",
					value: "89",
					change: "Same as last month",
					changeType: "neutral",
					icon: "👥",
					iconClass: "users",
				},
			],
			favoriteReports: [
				// {
				// 	title: "Employee Roster",
				// 	description: "Complete list of all active employees with contact information",
				// 	icon: "👥",
				// 	category: "employees",
				// 	lastGenerated: "2 hours ago",
				// },
				// {
				// 	title: "Financial Dashboard",
				// 	description: "Real-time financial KPIs and performance indicators",
				// 	icon: "📊",
				// 	category: "financial",
				// 	lastGenerated: "1 day ago",
				// },
				// {
				// 	title: "System Health Check",
				// 	description: "Monitor overall system performance and identify potential issues",
				// 	icon: "🔧",
				// 	category: "maintenance",
				// 	lastGenerated: "3 hours ago",
				// },
				// {
				// 	title: "Security Audit Report",
				// 	description: "Security incidents and compliance monitoring",
				// 	icon: "🔒",
				// 	category: "admin",
				// 	lastGenerated: "6 hours ago",
				// },
			],
			reportCategories: {
				financial: {
					title: "Financial",
					description: "Financial performance and accounting reports",
					icon: "💰",
					reports: [{ title: "Revenue Report" }, { title: "Expense Analysis" }, { title: "Profit & Loss Statement" }],
				},
				employees: {
					title: "Employees",
					description: "Employee management and HR reports",
					icon: "👥",
					reports: [{ title: "Employee Roster" }, { title: "Avibra Active Employees" }, { title: "Ghost Employees" }],
				},
				labor: {
					title: "Labor",
					description: "Labor management and scheduling reports",
					icon: "⏰",
					reports: [{ title: "Labor Hours Summary" }, { title: "Overtime Analysis" }, { title: "Scheduling Compliance" }],
				},
				stores: {
					title: "Stores",
					description: "Store operations and performance reports",
					icon: "🏪",
					reports: [{ title: "Store Performance Dashboard" }, { title: "Inventory Levels Report" }, { title: "Sales by Location" }],
				},
				maintenance: {
					title: "Maintenance",
					description: "System maintenance and operational reports",
					icon: "🔧",
					reports: [{ title: "System Health Check" }, { title: "Equipment Status Report" }],
				},
				training: {
					title: "Training",
					description: "Employee training and development reports",
					icon: "🎓",
					reports: [{ title: "Training Completion Report" }],
				},
				turnover: {
					title: "Turnover",
					description: "Employee turnover and retention analysis",
					icon: "🔄",
					reports: [{ title: "Turnover Rate Analysis" }, { title: "Exit Interview Summary" }],
				},
				vacation: {
					title: "Vacation",
					description: "Employee vacation and time-off management",
					icon: "🏖️",
					reports: [{ title: "Vacation Balance Report" }],
				},
				delivery: {
					title: "Delivery",
					description: "Delivery operations and logistics reports",
					icon: "🚚",
					reports: [{ title: "Delivery Performance" }, { title: "Route Optimization" }],
				},
			},
		};

		// Store dashboard data in state
		State.DashboardData = DashboardData;

		// DOM elements cache
		const Elements = {
			overviewGrid: document.getElementById("overviewGrid"),
			favoritesGrid: document.getElementById("favoritesGrid"),
			emptyFavorites: document.getElementById("emptyFavorites"),
			relevantCategoriesGrid: document.getElementById("relevantCategoriesGrid"),
			viewAllFavoritesBtn: document.getElementById("viewAllFavoritesBtn"),
			browseReportsBtn: document.getElementById("browseReportsBtn"),
			refreshChartsBtn: document.getElementById("refreshChartsBtn"),
		};

		// Initialize the dashboard application
		InitializeDashboard();

		/*
		 **
		 ** Initialize Dashboard Application
		 ** ===============================
		 */
		function InitializeDashboard() {
			// Set current job title from user context
			State.CurrentJobTitle = GetUserJobTitle();

			// Start loading
			TL.Loading.Start("main");
			TL.Agent({
				agent: ["app", "get-user-reports"],
				data: {},
				success(Result) {
					// Load all dashboard sections
					LoadOverviewSection();
					LoadFavoritesSection();
					LoadRelevantCategoriesSection();
					RenderChartsSection();

					// Setup event listeners
					InitializeEventListeners();
					TL.Loading.Stop("main");
				},

				//Agent returned an error, pass it to the user
				error(error) {
					TL.Notify.Banner("Error", error);
					TL.Loading.Stop("main");
				},
			});

			// // Load all dashboard sections
			// LoadOverviewSection();
			// LoadFavoritesSection();
			// LoadRelevantCategoriesSection();
			// RenderChartsSection();

			// // Setup event listeners
			// InitializeEventListeners();
		}

		/*
		 **
		 ** Initialize Event Listeners
		 ** =========================
		 */
		function InitializeEventListeners() {
			// Header action buttons
			if (Elements.viewAllFavoritesBtn) {
				Elements.viewAllFavoritesBtn.addEventListener("click", () => HandleAction("viewFavorites"));
			}
			if (Elements.browseReportsBtn) {
				Elements.browseReportsBtn.addEventListener("click", () => HandleAction("viewReports"));
			}
			if (Elements.refreshChartsBtn) {
				Elements.refreshChartsBtn.addEventListener("click", () => RenderChartsSection(true));
			}
		}

		/*
		 **
		 ** Render Charts Section
		 ** ====================
		 */
		function RenderChartsSection(IsRefresh = false) {
			// Example datasets
			const ReportsByCategory = {
				Financial: 12,
				Employees: 9,
				Labor: 8,
				Stores: 7,
				Delivery: 5,
			};

			const GeneratedLast7Days = (() => {
				const Labels = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
				const Values = [120, 135, 98, 142, 165, 90, 110];
				const Data = {};
				Labels.forEach((L, I) => (Data[L] = Values[I]));
				return Data;
			})();

			// Chart containers
			const ReportsByCategoryContainer = document.getElementById("reportsByCategoryChart");
			const GeneratedLast7DaysContainer = document.getElementById("reportsGeneratedChart");

			// Render charts via TL.Charts
			if (ReportsByCategoryContainer) {
				TL.Charts.Render({ Name: "Reports by Category", Type: "bar", Data: ReportsByCategory, FontSize: 12 }, "#reportsByCategoryChart");
			}

			if (GeneratedLast7DaysContainer) {
				TL.Charts.Render({ Name: "Reports Generated (Last 7 Days)", Type: "bar", Data: GeneratedLast7Days, FontSize: 12 }, "#reportsGeneratedChart");
			}

			if (!IsRefresh && window.TL && TL.DebugLog) {
				TL.DebugLog("Reporting Dashboard Charts Rendered", { ReportsByCategory, GeneratedLast7Days }, "#6366f1");
			}
		}

		/*
		 **
		 ** Get User Job Title
		 ** =================
		 */
		function GetUserJobTitle() {
			return window.TL && TL.User && TL.User.JobTitle ? TL.User.JobTitle : "Manager";
		}

		/*
		 **
		 ** Load Overview Section
		 ** ====================
		 */
		function LoadOverviewSection() {
			if (!Elements.overviewGrid) return;

			Elements.overviewGrid.innerHTML = "";
			State.DashboardData.overview.forEach((item) => {
				const card = CreateOverviewCard(item);
				Elements.overviewGrid.appendChild(card);
			});
		}

		/*
		 **
		 ** Load Favorites Section
		 ** =====================
		 */
		function LoadFavoritesSection() {
			if (!Elements.favoritesGrid) return;

			const favorites = State.DashboardData.favoriteReports;

			if (favorites.length === 0) {
				ShowEmptyFavorites();
				return;
			}

			ShowFavoritesGrid();
			RenderFavoriteReports(favorites);
		}

		/*
		 **
		 ** Load Relevant Categories Section
		 ** ===============================
		 */
		function LoadRelevantCategoriesSection() {
			if (!Elements.relevantCategoriesGrid) return;

			const selectedCategories = GetRelevantCategoriesForJobTitle(State.CurrentJobTitle);
			RenderRelevantCategories(selectedCategories);
		}

		/*
		 **
		 ** Get Relevant Categories for Job Title
		 ** ====================================
		 */
		function GetRelevantCategoriesForJobTitle(jobTitle) {
			// Job title to category mapping
			const jobCategoryMap = {
				Manager: ["financial", "employees", "labor", "stores"],
				HR: ["employees", "training", "turnover", "vacation"],
				Operations: ["maintenance", "delivery", "labor", "stores"],
				Accounting: ["financial", "labor", "stores", "employees"],
				Default: ["financial", "employees", "labor", "training"],
			};

			const selectedKeys = (jobCategoryMap[jobTitle] || jobCategoryMap.Default).slice(0, 4);
			const sourceCategories = State.DashboardData.reportCategories;

			return selectedKeys
				.map((key) => ({
					key,
					data: sourceCategories[key],
				}))
				.filter((item) => item.data);
		}

		/*
		 **
		 ** Show Empty Favorites State
		 ** =========================
		 */
		function ShowEmptyFavorites() {
			if (Elements.favoritesGrid) Elements.favoritesGrid.style.display = "none";
			if (Elements.emptyFavorites) Elements.emptyFavorites.style.display = "block";
		}

		/*
		 **
		 ** Show Favorites Grid
		 ** ==================
		 */
		function ShowFavoritesGrid() {
			if (Elements.favoritesGrid) Elements.favoritesGrid.style.display = "grid";
			if (Elements.emptyFavorites) Elements.emptyFavorites.style.display = "none";
		}

		/*
		 **
		 ** Render Favorite Reports
		 ** ======================
		 */
		function RenderFavoriteReports(favorites) {
			Elements.favoritesGrid.innerHTML = "";
			favorites.forEach((report) => {
				const card = CreateFavoriteCard(report);
				Elements.favoritesGrid.appendChild(card);
			});
		}

		/*
		 **
		 ** Render Relevant Categories
		 ** =========================
		 */
		function RenderRelevantCategories(categories) {
			Elements.relevantCategoriesGrid.innerHTML = "";
			categories.forEach(({ key, data }) => {
				const card = CreateCategoryCard(key, data);
				Elements.relevantCategoriesGrid.appendChild(card);
			});
		}

		/*
		 **
		 ** Create Overview Card Element
		 ** ===========================
		 */
		function CreateOverviewCard(item) {
			// Prepare template data
			const templateData = {
				IconClass: item.iconClass,
				Icon: item.icon,
				Title: item.title,
				Value: item.value,
			};

			// Get template and create element
			return CreateElementFromTemplate("Overview-Card", templateData);
		}

		/*
		 **
		 ** Create Favorite Report Card Element
		 ** ==================================
		 */
		function CreateFavoriteCard(report) {
			// Prepare template data for favorite card
			const templateData = {
				Category: report.category,
				Icon: report.icon,
				Title: report.title,
				Description: report.description,
				DescriptionClass: "",
				FavoriteClass: "favorited",
				FavoriteTitle: "Remove from favorites",
			};

			// Create card element
			const card = CreateElementFromTemplate("Report-Card", templateData);

			// Add favorite-card styling class
			card.classList.add("favorite-card");

			// Attach event listeners to card buttons
			AttachFavoriteCardEvents(card, report);

			return card;
		}

		/*
		 **
		 ** Create Category Card Element
		 ** ===========================
		 */
		function CreateCategoryCard(categoryKey, category) {
			// Prepare template data
			const templateData = {
				CategoryKey: categoryKey,
				Icon: category.icon || "📁",
				Title: category.title,
				Description: category.description,
				ReportCount: category.reports ? category.reports.length : 0,
			};

			// Create card element
			const card = CreateElementFromTemplate("Category-Card", templateData);

			// Attach event listeners to card
			AttachCategoryCardEvents(card, categoryKey);

			return card;
		}

		/*
		 **
		 ** Create Element from Template
		 ** ===========================
		 */
		function CreateElementFromTemplate(templateName, templateData) {
			const template = TL.Template(templateName);
			const filledTemplate = TL.FillTemplate(template, templateData);

			const tempDiv = document.createElement("div");
			tempDiv.innerHTML = filledTemplate;

			return tempDiv.firstElementChild;
		}

		/*
		 **
		 ** Attach Favorite Card Event Listeners
		 ** ===================================
		 */
		function AttachFavoriteCardEvents(card, report) {
			// Generate report button
			const generateBtn = card.querySelector(".generate-btn");
			if (generateBtn) {
				generateBtn.addEventListener("click", function () {
					GenerateReport(report.title);
				});
			}

			// Favorite button
			const favoriteBtn = card.querySelector(".favorite-btn");
			if (favoriteBtn) {
				favoriteBtn.addEventListener("click", function () {
					RemoveFromFavorites(report.title);
				});
			}
		}

		/*
		 **
		 ** Attach Category Card Event Listeners
		 ** ===================================
		 */
		function AttachCategoryCardEvents(card, categoryKey) {
			// Browse category button
			const browseBtn = card.querySelector(".browse-category-btn");
			if (browseBtn) {
				browseBtn.addEventListener("click", (e) => {
					e.stopPropagation();
					HandleAction("viewCategory:" + categoryKey);
				});
			}

			// Card click handler
			card.addEventListener("click", () => HandleAction("viewCategory:" + categoryKey));
		}

		/*
		 **
		 ** Handle Dashboard Actions
		 ** =======================
		 */
		function HandleAction(actionType) {
			TL.DebugLog("Dashboard Action:", actionType);

			switch (actionType) {
				case "viewReports":
					Navigation.ToReportsPage();
					break;
				case "viewFavorites":
					Navigation.ToFavoritesPage();
					break;
				default:
					if (actionType.startsWith("viewCategory:")) {
						const categoryKey = actionType.split(":")[1];
						Navigation.ToReporting(categoryKey);
						break;
					}
					TL.DebugLog("Unknown dashboard action:", actionType);
			}
		}

		/*
		 **
		 ** Generate Report
		 ** ==============
		 */
		function GenerateReport(reportTitle) {
			TL.DebugLog("Generate report requested:", reportTitle);
			// TODO: Implement actual report generation logic
			alert(`Generating report: ${reportTitle}`);
		}

		/*
		 **
		 ** Remove from Favorites
		 ** ====================
		 */
		function RemoveFromFavorites(reportTitle) {
			TL.DebugLog("Removing from favorites:", reportTitle);
			// TODO: Implement favorite removal logic
			alert(`Removing from favorites: ${reportTitle}`);
		}
	});
});
