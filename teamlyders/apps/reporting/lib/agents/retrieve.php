<?php

// Unpack Reporting Engine
$TL->Unpack(['ReportingEngine' => ['ReportingEngine']]);

// Get the post data
$POST = $TL->Agent->Input;

// Verify we have a Report Name.. if not we cannot proceed
if (empty($POST['ReportName']))
    $TL->Agent->Error("Invalid parameters");

// Retrieve the report data
// IMPORTANT:  We also want to forward any other Params sent by the client
if (!$Result = $TL->ReportingEngine->Retrieve($POST)) {
    $TL->Agent->Error($TL->ReportingEngine->Error);
}
// Send back to client
$TL->Agent->Exit($Result);
