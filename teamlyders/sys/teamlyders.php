<?php
namespace System;



class TeamLyders extends Utilities{

    protected $Packages;

    public $API;
    public $User;
    public $Agent;
    public $Stores;
    public $Session;
    public $SFTP;
    public $Periods;
    public $Phone;
    public $Mail;
    public $Alignments;
    public $Messages;
    public $DOM;
    public $Database;
    public $Cache;
    public $Authenticator;
    public $Files;
    public $Calendar;
    public $Worklist;
    public $Curl;
    public $Backblaze;
    public $Documents;
    public $Location;
    public $Correctives;
    public $DPoll;
    public $Lists;
    public $Bonus;
    public $Automation;
    public $Tasks;
    public $InstantPay;
    public $BackgroundChecks;
    public $Analytics;
    public $Site;
    public $Liberty;
    public $Pharmacy;
    public $Shopify;
    public $Downloader;
    public $BrowserUtility;
    public $ReportingEngine;


    public function __construct( $Packages = false ){ 

        // Initiate Timezone
        $this->InitTimezone();
                 
        // Initiate Locality
        $this->InitLocality();
        
        // Initiate Database
        $this->InitDatabase();

        // Initiate Session
        $this->InitSession();

        // Initiate Site Conig
        $this->InitSiteConfig();

        // Verify Permissions
        $this->ValidateAccess();

        // Initiate Default Packages
        $this->InitPackages();
    }

    /*
    **
    **
    **
    **
    **
    **
    ** Initiate Timezone
    ** ======================================
    */

    private function InitTimezone(){

        // Establish Default Timezone
        date_default_timezone_set('America/Detroit');

    }
    
    /*
    **
    **
    **
    **
    **
    **
    ** Initiate Locality
    ** ======================================
    */

    private function InitLocality(){

        $this->Location = [];

        // Initialize serverLevel
        $this->Location['serverLevel'] = (php_sapi_name() === 'cli');

        // Set apiLevel to false as a placeholder
        $this->Location['apiLevel'] = false;

        // Define if we are using an SSL certificate
        $this->Location['ssl'] = (!$this->Location['serverLevel']) ? (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] != 'off') : false;

        // File system root relative to our current location
        $this->Location['root'] = dirname(__DIR__, 2)."/";
        
        // Domain root URL
        $this->Location['base'] = (!$this->Location['serverLevel']) ? 'http'.(($this->Location['ssl']) ? 's' : '').'://'.$_SERVER['SERVER_NAME'].'/' : false;
        
        // Domain root URL
        $this->Location['title'] = $_SERVER['Title'] ?? 'UNKNOWN';

        // Domain name only - includes sub-domain
        $this->Location['domain'] = (!$this->Location['serverLevel']) ? substr($_SERVER['HTTP_HOST'], 0, strrpos($_SERVER['HTTP_HOST'], '.')) : 'SERVER';
        
        // Domain name extension
        $this->Location['domainExtension'] = (!$this->Location['serverLevel']) ? explode(".",$_SERVER['HTTP_HOST'])[count(explode(".",$_SERVER['HTTP_HOST']))-1] : 'co';

        // Brand of current website
        $this->Location['brand'] = (isset($_SERVER['BRAND'])) ? $_SERVER['BRAND'] : false;
        
        // Shortcut - apps directory
        $this->Location['apps'] = $this->Location['root'].'teamlyders/apps/';

        // Shortcut - Packages directory
        $this->Location['pkg'] = $this->Location['root'].'teamlyders/pkg/';

        // Shortcut - System directory
        $this->Location['sys'] = $this->Location['root'].'teamlyders/sys/';

        // Shortcut - Public lib
        $this->Location['lib'] = $this->Location['root'].'public/lib/';

        // Shortcut - Temp lib
        $this->Location['tmp'] = $this->Location['root'].'tmp/';

        // Shortcut - document templates
        $this->Location['documents'] = $this->Location['lib'].'packages/documents/documents/';

        // Shortcut - automation tasks
        $this->Location['automationTasks'] = $this->Location['root'].'teamlyders/pkg/automation/tasks/';

        // Shortcut - document templates
        $this->Location['worklists'] = $this->Location['lib'].'packages/worklist/worklists/';
        
        // Name of page we're currently in
        $this->Location['page'] = pathinfo($_SERVER['PHP_SELF'], PATHINFO_FILENAME);
            
        // User agent of user
        $this->Location['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? false;     
        
        // Formatted user agent of user
        $this->Location['parsed_user_agent'] = false;//$this->ParseUserAgent();  
        
    }

    /*
    **
    **
    **
    **
    **
    **
    ** Initiate Database Package
    ** ======================================
    */

    private function InitDatabase(){

        $this->Unpack([ 'System'=>['Database'] ]);

    }
    
    /*
    **
    **
    **
    **
    **
    **
    ** Session Controller
    ** ======================================
    */

    private function InitSession(){
        
        // Include session package in TL object
        $this->Session = new \System\Session($this);
  
    }

    /*
    **
    **
    **
    **
    **
    **
    ** Site Config
    ** ======================================
    */
    public function InitSiteConfig(){
        // Unpack Site package to use
        $this->Unpack([ 'System'=>['Site'] ]);

        // Load Site Config, ignore if server level is true
        if(!$this->Site->GetConfig() && $this->Location['serverLevel'] !== true){
            $this->Log('Failed to load config for site config for URL: '.$this->Location['domain'], 'Error');
            $this->Kill();
            exit("Well this is awkward! An error occurred - Please refresh the page to continue - Code: LA95B");
        }
    }


    /*
    **
    **
    **
    **
    **
    **
    ** Initiate Default Packages
    ** ======================================
    */  

    public function InitPackages( $Packages = false ){

        $this->Unpack([ 'System'=>['DOM', 'Cache', 'User', 'Stores', 'Mail', 'Messages'] ]);

    }
    
    /*
    **
    **
    **
    **
    **
    **
    ** Validate User's Access
    ** ======================================
    */

    private function ValidateAccess(){               
        // Establish shortcut variables for system timeouts
        // Convert the timeout values from minutes to seconds
        $ActiveTimeout = round(\Config\System::ActiveTimeout * 60);
        $InactiveTimeout = round($this->Site->GetConfig('Timeout Length') * 60);

        // Ensure that we are successfully able to retrieve the sessions info
        // Note: This will return false if the user logged in from another browser
        if( !$SessionInfo = $this->Session->Info() ){
            $this->Kill();
            exit("Well this is awkward! An error occurred - Please refresh the page to continue - Code: SN25B");
        }

        // Ensure that the required parameters exist
        if( empty($SessionInfo['Last Active']) || empty($SessionInfo['Last Active']) || empty($SessionInfo['Created']) ){
            $this->Kill();
            exit("Well this is awkward! An error occurred - Code: SN25C");
        }

        $LastActive = $SessionInfo['Last Active'];
        $TimeCreated = $SessionInfo['Created'];

        // Check if the user has surpassed their timeout for being inactive
        if( time() - strtotime($LastActive) > $InactiveTimeout ){ 
            $this->Kill();
        }

        // Check if the user has surpassed their timeout for being active
        if( time() - strtotime($TimeCreated) > $ActiveTimeout ){
            $this->Kill();
        }
    }
    
    /*
    **
    **
    **
    **
    **
    **
    ** Unpack Sub-Systems
    ** ======================================
    */  

    public function Unpack( $Packages = false ){  

        // Define packages globally in class
        $this->Packages = $Packages;
            
        // Validate package requests
        if( !$this->Packages || gettype($this->Packages) !== 'array' || count($this->Packages) === 0 ){ return false; }
        
        // Iterate through packages
        foreach( $this->Packages as $Package=>$Controllers ){

            // Iterate through controllers in packages
            foreach( $Controllers as $Controller ){

                // Check if this class has already been added to TL
                if( isset($this->{$Controller}) ){ continue; }
        
                // All packages will be in the system namespace
                $Class = '\\'.$Package.'\\'.$Controller; //if( $Package === 'User' ){ exit($Class); }

                // Establish the class globally in TL
                $this->{$Controller} = new $Class($this); 

            }
        }
    } 
  
    /*
    **
    **
    **
    **
    **
    **
    ** Kill System Session
    ** ======================================
    */ 

    public function Kill( $link = false ){
             
        // Delete the session from the database
        $this->Database->Query("DELETE FROM `Sessions` WHERE `Session ID` = :SessionID", [ ':SessionID'=>$this->Session->ID() ]); 
         
        // Open session for writting
        if( session_status() == PHP_SESSION_NONE ){ session_start(); }
        
        // Remove all data from the session super global
        $_SESSION = NULL;

        // Kill the session!
        session_unset();
        session_destroy();
        session_write_close();

        // Flush output buffering
        ob_start();
        
        // Redirect if that was requested
        if( $link ){ $this->Redirect($link); }
        
        // Return true to say everything is all good :)
        return true;
    }    
       
}